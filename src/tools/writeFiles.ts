import { z } from "zod";
import { writeFileSync } from "fs";
import { join } from "path";
import { tool } from "@langchain/core/tools";

// 定义输入参数的 schema
const writeFileSchema = z.object({
  filename: z.string().describe("文件名，例如：weather.txt, report.md 等"),
  content: z.string().describe("要写入文件的内容"),
});

// 使用 @langchain/core/tools 的 tool 函数定义工具
export const writeFileTool = (tool as any)(
  async (input: { filename: string; content: string }) => {
    const { filename, content } = input;
    console.log(`Writing to file: ${filename}`);

    try {
      // Write to a files directory in the demo app
      const filePath = join(process.cwd(), "files", filename);

      // Create directory if it doesn't exist
      const { mkdirSync, existsSync } = await import("fs");
      const { dirname } = await import("path");
      const dir = dirname(filePath);
      if (!existsSync(dir)) {
        mkdirSync(dir, { recursive: true });
      }

      writeFileSync(filePath, content, "utf8");
      return `文件已成功写入：${filePath}\n内容长度：${content.length}字符`;
    } catch (error) {
      return `写入文件失败：${error instanceof Error ? error.message : "未知错误"}`;
    }
  },
  {
    name: "write_file",
    description:
      "将内容写入到指定文件中。当用户要求保存信息到文件时使用此工具。",
    schema: writeFileSchema as z.ZodTypeAny,
  }
);

export const tools = [writeFileTool];
