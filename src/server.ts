import path from 'path';
import { fileURLToPath } from 'url';

// ESM 等价路径工具（如需）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function main() {
  // 使用绝对路径导入 startServer 函数
  const serverPath = path.join(process.cwd(), 'node_modules/@langchain/langgraph-api/dist/server.mjs');
  const { startServer } = await import(serverPath);
  
  // 使用 startServer 函数启动服务器
  const server = await startServer({
    port: 2024,
    host: "0.0.0.0",
    nWorkers: 1,
    cwd: process.cwd(),
    graphs: {
      "designToCode": "./dist/graph/designToCode/index.js:graph",
      "simpleChat": "./dist/graph/simpleChat/index.js:graph",
    }
  });

  console.log(`Server started at ${server.host}`);
}

// 直接启动（ESM 下无 require.main 判断）
main().catch(console.error);
