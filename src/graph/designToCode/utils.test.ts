import { AIMessage } from "@langchain/core/messages";
import {
  parseModelOutput,
  extractFileContentFromToolResult,
  extractAllFileContentsFromToolResult,
  hasToolCalls,
  getToolCallNames,
  hasSpecificToolCall,
} from "./utils.js";

describe("Model Output Utils", () => {
  describe("parseModelOutput", () => {
    it("应该解析字符串内容", () => {
      const message = new AIMessage("Hello World");
      const result = parseModelOutput(message);
      expect(result).toBe("Hello World");
    });

    it("应该解析多模态内容", () => {
      const message = new AIMessage("", {
        content: [
          { type: "text", text: "Hello" },
          { type: "text", text: " World" },
        ],
      });
      const result = parseModelOutput(message);
      expect(result).toBe("Hello World");
    });

    it("应该解析对象内容", () => {
      const content = { message: "Hello", code: "console.log('test')" };
      const message = new AIMessage("", { content });
      const result = parseModelOutput(message);
      expect(result).toBe(JSON.stringify(content));
    });

    it("应该处理空消息", () => {
      const result = parseModelOutput(null as any);
      expect(result).toBe("");
    });
  });

  describe("extractFileContentFromToolResult", () => {
    it("应该提取指定类型的文件内容", () => {
      const message = new AIMessage("", {
        tool_calls: [
          {
            id: "1",
            name: "writeFile",
            args: JSON.stringify({
              fileName: "style.css",
              content: "body { color: red; }",
            }),
          },
        ],
      });
      const result = extractFileContentFromToolResult(message, ".css");
      expect(result).toBe("body { color: red; }");
    });
  });

  describe("extractAllFileContentsFromToolResult", () => {
    it("应该提取所有文件内容", () => {
      const message = new AIMessage("", {
        tool_calls: [
          {
            id: "1",
            name: "writeFile",
            args: JSON.stringify({
              fileName: "index.html",
              content: "<html>Hello</html>",
            }),
          },
          {
            id: "2",
            name: "writeFile",
            args: JSON.stringify({
              fileName: "style.css",
              content: "body { color: red; }",
            }),
          },
        ],
      });
      const result = extractAllFileContentsFromToolResult(message);
      expect(result).toEqual({
        "index.html": "<html>Hello</html>",
        "style.css": "body { color: red; }",
      });
    });
  });

  describe("hasToolCalls", () => {
    it("应该检测到工具调用", () => {
      const message = new AIMessage("", {
        tool_calls: [{ id: "1", name: "test", args: "{}" }],
      });
      expect(hasToolCalls(message)).toBe(true);
    });

    it("应该检测到没有工具调用", () => {
      const message = new AIMessage("Hello");
      expect(hasToolCalls(message)).toBe(false);
    });
  });

  describe("getToolCallNames", () => {
    it("应该获取工具调用名称列表", () => {
      const message = new AIMessage("", {
        tool_calls: [
          { id: "1", name: "writeFile", args: "{}" },
          { id: "2", name: "readFile", args: "{}" },
        ],
      });
      const result = getToolCallNames(message);
      expect(result).toEqual(["writeFile", "readFile"]);
    });
  });

  describe("hasSpecificToolCall", () => {
    it("应该检测到特定工具调用", () => {
      const message = new AIMessage("", {
        tool_calls: [{ id: "1", name: "writeFile", args: "{}" }],
      });
      expect(hasSpecificToolCall(message, "writeFile")).toBe(true);
      expect(hasSpecificToolCall(message, "readFile")).toBe(false);
    });
  });
});
