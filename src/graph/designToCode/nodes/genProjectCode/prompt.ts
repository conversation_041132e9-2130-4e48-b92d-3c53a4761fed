// 项目代码生成系统提示
export function getGenProjectCodeSystemPrompt(): string {
  return `你是一个专业的全栈开发专家，专门负责将 HTML 设计稿转换为完整的项目代码。

代码生成要求：
1. 基于提供的 HTML 内容生成完整的项目结构
2. 包含前端和后端代码
3. 使用现代化的技术栈（React/Vue + Node.js/Express）
4. 确保代码结构清晰，易于维护
5. 包含必要的配置文件（package.json, README.md 等）
6. 实现响应式设计
7. 包含基本的交互功能
8. 遵循最佳实践和代码规范
9. 确保项目可以直接运行

请生成完整的项目代码，包括所有必要的文件和配置。`;
}

// 项目代码生成用户提示模板
export function getGenProjectCodeUserPrompt(combinedHtml: string): string {
  return `请基于以下 HTML 内容生成一个完整的项目代码：

${combinedHtml}

请生成包含以下内容的完整项目：
1. 前端代码（HTML, CSS, JavaScript/TypeScript）
2. 后端代码（Node.js/Express）
3. 项目配置文件（package.json, tsconfig.json 等）
4. 说明文档（README.md）
5. 必要的依赖和脚本

请确保生成的代码可以直接运行，并且具有良好的结构和可维护性。`;
}

// 错误处理提示
export function getGenProjectCodeErrorPrompt(error: string): string {
  return `项目代码生成过程中出现错误：${error}

请检查：
1. HTML 内容是否完整
2. 是否有语法错误
3. 是否有缺失的依赖
4. 是否有配置问题

请提供修复建议或重新尝试生成。`;
}

// 验证提示
export function getGenProjectCodeValidationPrompt(htmlContent: string): string {
  return `验证项目代码生成输入：
- HTML 内容长度：${htmlContent.length} 字符
- 是否包含 HTML 标签：${htmlContent.includes("<") ? "是" : "否"}
- 是否包含样式：${htmlContent.includes("style") || htmlContent.includes("css") ? "是" : "否"}
- 是否包含脚本：${htmlContent.includes("script") || htmlContent.includes("js") ? "是" : "否"}

请确认是否继续生成项目代码。`;
}
