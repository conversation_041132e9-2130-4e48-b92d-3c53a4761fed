import {
  StateGraph,
  MemorySaver,
  END,
  START,
  Annotation,
} from "@langchain/langgraph";
import {
  BaseMessage,
  SystemMessage,
  HumanMessage,
  AIMessage,
} from "@langchain/core/messages";
import { CombineHtmlState } from "./types.js";
import { createModel } from "../../model/index.js";
import {
  getCombineHtmlSystemPrompt,
  getCombineHtmlUserPrompt,
} from "./prompt.js";
import { formatHtmlFragmentsForLLM } from "./utils.js";
import { RefactoredDesignItem } from "../../types.js";

const checkpointer = new MemorySaver();

// 状态对象定义 - 使用 Annotation 模式
const CombineHtmlStateAnnotation = Annotation.Root({
  input: Annotation<RefactoredDesignItem[]>({
    reducer: (x: RefactoredDesignItem[], y: RefactoredDesignItem[]) => y,
    default: () => [],
  }),
  output: Annotation<string>({
    reducer: (x: string, y: string) => y,
    default: () => "",
  }),
  messages: Annotation<BaseMessage[]>({
    reducer: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  }),
  error: Annotation<string | undefined>({
    reducer: (x: string | undefined, y: string | undefined) => y,
    default: () => undefined,
  }),
  status: Annotation<string>({
    reducer: (x: string, y: string) => y,
    default: () => "running",
  }),
});

// 定义状态类型
type CombineHtmlStateType = typeof CombineHtmlStateAnnotation.State;
type CombineHtmlUpdateType = typeof CombineHtmlStateAnnotation.Update;

/**
 * 验证输入条件边函数 - 检查输入数据的有效性
 */
function validateInput(
  state: CombineHtmlStateType
): "combineHtml" | "finalCheck" {
  console.log("CombineHtml: 验证输入...");

  if (!state.input || !Array.isArray(state.input) || state.input.length === 0) {
    console.log("CombineHtml: 输入验证失败 - 没有提供有效的 HTML 结果");
    return "finalCheck";
  }

  console.log(
    `CombineHtml: 输入验证通过 - 有 ${state.input.length} 个 HTML 结果`
  );
  return "combineHtml";
}

/**
 * 合并 HTML 节点 - 使用 LLM 智能合并
 */
async function combineHtmlNode(
  state: CombineHtmlStateType
): Promise<CombineHtmlUpdateType> {
  console.log("CombineHtml: 开始使用 LLM 合并 HTML...");
  // 使用提示模板
  const systemPrompt = getCombineHtmlSystemPrompt();

  if (state.input.length === 1) {
    state.output = state.input[0].refactoredHtml;
    return {
      status: "completed",
      messages: [
        new SystemMessage(systemPrompt),
        new AIMessage(
          `CombineHtml: 合并完成，合并了 ${state.input.length} 个结果`
        ),
        new AIMessage(state.input[0].refactoredHtml),
      ],
    };
  }

  // 创建 LLM 模型
  const model = createModel();

  const htmlFragments = formatHtmlFragmentsForLLM(state.input);
  const userPrompt = getCombineHtmlUserPrompt(
    htmlFragments,
    state.input.length
  );

  console.log(`CombineHtml: 调用 LLM 合并 ${state.input.length} 个 HTML 片段`);

  // 调用 LLM
  const response = await model.invoke([
    new SystemMessage(systemPrompt),
    new HumanMessage(userPrompt),
  ]);

  const combinedHtml = response.content as string;

  console.log("CombineHtml: LLM 合并完成");

  return {
    output: combinedHtml,
    status: "completed",
    messages: [
      new SystemMessage(systemPrompt),
      new HumanMessage(userPrompt),
      new AIMessage(combinedHtml),
    ],
  };
}

/**
 * 最终检查节点：作为流程的"门卫"
 * 检查状态，如果发现错误则抛出异常，否则正常结束
 */
async function finalCheck(
  state: CombineHtmlStateType
): Promise<CombineHtmlUpdateType> {
  console.log("CombineHtml: 执行 finalCheck 节点...");

  // 检查是否有输入验证错误
  if (!state.input || !Array.isArray(state.input) || state.input.length === 0) {
    const errorMsg = "输入验证失败：没有提供有效的 HTML 结果";
    console.error(`CombineHtml: ${errorMsg}`);
    return {
      status: "failed",
      error: errorMsg,
      messages: [new SystemMessage(`CombineHtml 执行失败: ${errorMsg}`)],
    };
  }

  // 检查是否有其他错误
  if (state.error) {
    console.error("CombineHtml: 检测到状态中的错误信息。");
    return {
      status: "failed",
      messages: [new SystemMessage(`CombineHtml 执行失败: ${state.error}`)],
    };
  }

  console.log("CombineHtml: 状态正常，流程可以成功结束。");
  return {
    status: "completed",
    messages: [new SystemMessage("CombineHtml 执行成功")],
  };
}

// 构建 CombineHtml 工作流图
const workflow = new StateGraph(CombineHtmlStateAnnotation)
  .addNode("combineHtml", combineHtmlNode)
  .addNode("finalCheck", finalCheck)
  .addConditionalEdges(START, validateInput, {
    combineHtml: "combineHtml",
    finalCheck: "finalCheck",
  })
  .addEdge("combineHtml", "finalCheck")
  .addEdge("finalCheck", END);

// 编译 CombineHtml 工作流
export const graph = workflow.compile({ checkpointer });
graph.name = "combineHtml";

// 导出类型
export type { CombineHtmlState, RefactoredDesignItem };
export type { CombineHtmlStateType, CombineHtmlUpdateType };
