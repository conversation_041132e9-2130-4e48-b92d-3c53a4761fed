# 代码重构检查清单 (Refactoring Checklist)

## 一、 核心准则 (Highest Principles)

- [ ] **内容完整性**: 原始 HTML 中的所有**可见文本**、**图片地址**和**链接地址**是否都 100% 保留在了新代码中？图片地址以`http://************:8089/` 开头？
- [ ] **响应式布局**: 是否实现了**完全流式**的响应式布局，确保页面在所有设备宽度都能自适应？
- [ ] **页面根元素**: 页面的根容器是否为 `<div id="app">`，其样式是否固定为 `display: flex; flex-direction: column; min-height: 100vh;`, 不允许有其他样式，且其布局能随视口宽度自由伸展？
- [ ] **页面背景**: 所有页面级的背景图、背景色等视觉样式是否都直接应用在了 `<body>` 标签上？
- [ ] **页面根结构**: 页面根结构是否严格遵循了 `#app > .navHeader + .main` 的布局？
- [ ] **NavHeader 归属**: `.navHeader` 的高度是否**固定**为 50px？并且，所有在视觉上位于这 50px 垂直空间内的 UI 元素，是否都已在 HTML 结构上归属于 `.navHeader`？
- [ ] **禁止宽度限制**: 是否**严禁**在页面级容器（如 `#app`）上使用 `width` 或 `max-width` 限制页面伸展？
- [ ] **375px 基准**: 所有样式（间距、字号等）是否以在 375px 视口下精准还原设计稿为目标进行校准？
- [ ] **清晰性优先**: 是否避免了过度抽象？当复用导致数据结构复杂或可读性降低时，是否优先选择了更清晰直观的实现？

## 二、 编码规范与禁止项 (Coding Standards & Prohibitions)

### 命名规范

- [ ] **CSS 类名**: 是否所有 CSS 类名都严格使用了 `camelCase` (小驼峰) 命名法？
- [ ] **JavaScript 命名**: 是否所有 JS 变量和函数名都严格使用了 `camelCase` (小驼峰) 命名法？
- [ ] **JS 语义化**: JS 变量和属性名是否具有清晰的业务含义，严禁出现如 `item1`, `title2` 等无意义命名？
- [ ] **JS 术语精确性**: 是否深入理解业务，精准区分了易混淆的术语？(例如，在“我的关注”页面中，列表数据应命名为 `watchlist` 而非 `holdings`)

### CSS 编写规则

- [ ] **禁止内联样式**: HTML 元素上是否**没有任何** `style="..."` 属性？
- [ ] **样式位置**: 是否所有 CSS 规则都定义在唯一的 `<style>` 标签内？
- [ ] **禁止框架**: 是否没有使用任何外部 CSS 框架（如 Bootstrap）？

### Vue.js 使用限制

- [ ] **Vue.js 引入方式**: 是否通过指定的 `<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>` 标签引入 Vue.js？
- [ ] **功能合规性**: 是否**仅**使用了 `v-for`, 文本插值 (`{{...}}`) 和属性绑定 (`:`) 这三项功能？
- [ ] **禁止项**: 是否**没有**使用计算属性 (`computed`)、方法 (`methods`)、生命周期钩子、`v-show`, `v-html` 或自定义 Vue 组件？
- [ ] **`v-if` 使用**: `v-if` 是否**仅**用于处理**单个业务组件内部**的状态变化，而**没有**用于切换不同的业务组件？

## 三、 结构与内容 (Structure & Content)

- [ ] **功能分析前置**: 在动手编码前，是否已对UI元素的**真实业务功能**进行了分析，而非仅凭视觉猜测？

### 组件化

- [ ] **业务组件拆分**: 是否已将不同业务语境的模块（如"资产总览卡片"、"持仓列表"）在 HTML 结构上分离，并添加了标准格式的注释 `<!-- 业务组件：... -->`？
- [ ] **禁止逻辑混合**: 是否**严禁**将业务逻辑不同（如"股票"和"基金"）的模块合并到同一个 `v-for` 循环中渲染？

### 数据与视图分离

- [ ] **静态标签硬编码**: UI 中不会改变的文本（如卡片标题 "股票"、字段名 "持仓总值"）是否在 HTML 中硬编码？
- [ ] **动态数据模型化**: 所有需要动态变化的数据（如总资产、收益、持仓列表数组）是否都定义在 Vue 的 `data` 对象中？
- [ ] **数据结构纯粹性**: `v-for` 循环的数据源是否为纯粹的数据对象数组，**严禁**将静态标签文本（如 "市值/数量"）混入业务数据中？
- [ ] **数据纯净性**: 数据模型中是否**不包含**任何用于视觉表现的符号（如文本截断产生的 "..."）？
- [ ] **数据结构按需定义**: 数据模型是否精确反映了UI的真实结构？（例如，仅在UI真实存在摘要区域时，才定义 `summary` 对象）

### 内容与布局

- [ ] **布局现代化**: 是否已使用 Flexbox（`display: flex`, `justify-content`, `gap`）替换了所有基于 `margin` 的定位布局？
- [ ] **微观布局复现**: 在重构包含多个子元素的复杂UI控件（如按钮）时，是否同样使用了 `justify-content: space-between` 等技术来精确复现其**内部布局**，以防止意外换行？
- [ ] **禁止 `width: 100%` 拉伸**: 在 Flexbox 或 Grid 布局中，是否**严禁**使用 `width: 100%` 拉伸子项，并改用 `flex: 1` 或 `fr` 单位？
- [ ] **结构精简**: 是否移除了不必要的 `<div>` 包装元素，降低了 HTML 嵌套深度？
- [ ] **图形占位符**: 是否所有图表类组件（如折线图、K线图）都已替换为带描述性文案的占位符 `<div>`？
- [ ] **严禁系统 UI**: 代码中是否已**彻底移除**了所有硬编码的移动设备系统 UI 元素（如顶部的状态栏、底部的 Home Indicator）？
- [ ] **禁止语义化标签**: 是否**严禁**使用 HTML5 语义化标签（如 `<header>`, `<main>`），并确保所有结构都由 `<div>` 构建？
