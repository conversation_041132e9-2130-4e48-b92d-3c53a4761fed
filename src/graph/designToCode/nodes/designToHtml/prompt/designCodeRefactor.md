# html 代码精简重构计划

## 1. 重构目标与范围

### 1.1. 核心目标

在 html 单文件内部，通过代码优化，提升其可维护性和健壮性。

### 1.2. 最高准则

1.  **100% 内容保留**：原始文件中的所有**可见文本**、**图片地址**和**链接地址**都必须原封不动地出现在新代码中。
2.  **响应式优先，375px 为校准基准 (Responsive-First, with 375px as Calibration Baseline)**：**最高优先级是实现一个完全流式的响应式布局**，确保页面在任何设备宽度（从小型手机到超宽桌面）上都能自适应展示。**375px 视口仅作为视觉校准的基准**：所有样式（如间距、字号）的设定应以在 375px 宽度下精准还原设计稿为目标。一旦校准完成，这些样式必须能在流式布局中平滑地适应所有尺寸，**严禁使用 `max-width` 等属性限制页面的整体伸展**。
3.  **页面根结构与组件归属原则 (Page Root Structure & Component Ownership Principle)**:
    - **结构定义**: 页面根结构必须严格遵循 `#app > .navHeader + .main` 的布局。
    - **`.navHeader` 定义与范围**: `.navHeader` 用于承载顶部导航。其**高度必须严格固定为 50px**。此高度不仅是样式约束，更是**决定组件归属的核心架构准则 (Core Architectural Rule)**：在对原始设计稿进行分析时，任何在视觉上完全或部分位于这 50px 垂直空间内的 UI 元素，**都必须**被视为顶部导航的一部分，并**必须**在重构后的 HTML 结构中被归入 `.navHeader` 容器。
    - **`.main` 定义**: `.main` 用于承载所有 50px 导航栏下方的业务组件。
    - **`#app` 样式固定**: 页面根元素 `#app` 的样式必须**绝对固定**，仅包含且只包含 `display: flex; flex-direction: column; min-height: 100vh;`。**严禁**为 `#app` 添加任何其他 CSS 属性。
    - **视觉样式归属**: 所有页面级的视觉样式（如背景图、背景色）**必须**直接应用于 `<body>` 标签。**严禁**为了视觉效果（如实现共享背景）而创建额外的 `<div>` 容器来包裹多个独立的业务组件，破坏组件的结构独立性。

    **代码示例**:

    ```html
    <style>
      .navHeader {
        height: 50px;
        /* ... 其他导航栏样式 ... */
      }
    </style>
    <body>
      <div id="app">
        <!-- 1. 顶部导航栏组件 (固定高度 50px) -->
        <div class="navHeader">
          <!-- ... 导航栏内容 ... -->
        </div>

        <!-- 2. 主体内容区域 -->
        <div class="main">
          <!-- ... 各个业务组件 ... -->
        </div>
      </div>
      <script>
        // ... Vue ...
      </script>
    </body>
    ```

4.  **清晰性优于"零重复" (Clarity over DRY)**: 当抽象和复用（如组件归一）会显著增加数据结构的复杂性或降低代码可读性时，应优先选择保持代码的清晰直观。**宁可接受少量、带清晰注释的 HTML 结构重复，也不要创造一个难以理解的、过度抽象的数据模型。**

### 1.3. 重构范围 (In Scope)

1.  **布局重构**: 使用现代 CSS 布局（Flexbox）替换所有基于固定边距（`margin`）的"魔法数字"布局。
2.  **代码精简**: 移除冗余的 HTML 包装元素，并合并功能和样式完全相同的 CSS 类。
3.  **业务组件拆分 (Business Component Splitting)**: 识别页面中具有**独立业务语境**的模块，即使它们在视觉上相似。每个模块都应被视为一个独立的业务组件，并在 HTML 中通过清晰的注释进行物理隔离。**严禁**将业务逻辑不同（如'股票'和'基金'）的模块合并到一个 `v-for` 循环中。
4.  **通用 UI 组件识别 (Common UI Component Identification)**: 除业务组件外，还需识别并分离出页面中可复用的、具有通用 UI 职责的组件（如已在根结构中约定的 `.navHeader`）。
5.  **响应式布局**: 采用移动端优先的策略，移除固定 `width`，通过流式布局和可读性宽度约束，实现页面在手机、平板和桌面等不同设备上的自适应显示。
6.  **引入轻量级 MVVM**: 针对**单个业务组件内部**的重复性列表结构，通过 CDN 引入 `Vue`，并使用其**核心模板功能**（如 `v-for`, 文本插值）进行渲染，以减少该组件内的 DOM 数量。
7.  **组件区域注释**: 在 HTML 代码中，通过注释明确标识出各个独立的业务组件区域。这有助于提高代码的可读性，方便其他开发者快速理解页面结构。注释应采用统一格式，例如：`<!-- 业务组件：股票持仓卡片 -->`。

### 1.4. 非目标范围 (Out of Scope)

为确保焦点，本次重构**不包含**以下工作：

- 拆分 CSS 到独立文件。
- 资源本地化（图片、字体等）。
- 可访问性（Accessibility）优化。

### 1.5. 编码规范与禁止项 (Coding Standards & Prohibitions)

为确保重构目标的纯粹性、可维护性和一致性，在实施过程中需严格遵守以下规则：

1.  **命名规范 (Naming Conventions)**
    - **CSS 类名驼峰规范**: 为确保命名风格统一，所有 CSS 类名必须使用 `camelCase` (小驼峰) 命名法。例如：`.assetCard`, `.totalAssetsHeader`。
    - **JS 驼峰规范**: 所有在 `<script>` 标签中定义的 JavaScript 变量和函数名，必须使用 `camelCase` (小驼峰) 命名法。例如：`let stockHoldings = [];`。
    - **JS 语义化命名**: 所有 JavaScript 变量和属性名必须具有清晰的业务含义，准确描述其内容。严禁使用如 `title1`, `col2`, `itemL1` 等无意义或序列化的命名。

2.  **CSS 编写规则**
    - **禁止内联样式**: 严禁在 HTML 元素上使用 `style` 属性。
    - **样式位置**: 所有样式必须在 `<style>` 标签内定义，禁止使用外部 CSS 文件。
    - **禁止使用框架与设计系统**: 禁止使用任何 CSS 框架（如 Bootstrap）。

3.  **Vue.js 使用限制**
    - **核心功能许可**: 为实现数据与视图的分离，仅允许使用以下 Vue.js 核心模板功能：
      - **`v-for`**: 用于渲染列表。
      - **文本插值 (`{{ ... }}`)**: 用于在模板中显示 `data` 对象中的数据。
      - **属性绑定 (`v-bind:` 或 `:`)**: 用于动态设置 HTML 属性，如 `:class` 或 `:src`。
    - **数据隔离与结构**:
      - 每个独立的业务组件（如股票卡片、基金卡片）都应有自己独立的根数据对象。
      - **数据模型必须精确反映UI**: 数据结构的设计应严格基于视觉设计稿中**实际存在**的元素。**严禁**为设计稿中不存在的视觉元素（如摘要、页脚）在数据模型中预创建空的占位对象。
      - **推荐**使用**嵌套对象**来组织数据，将同一业务组件的摘要数据 (`summary`) 和列表数据 (`list`, `items`, `watchlist` 等) 归纳在一起，以提升数据模型的可读性和业务内聚性。
    - **`v-if` 使用规范**: 允许在单个业务组件内部使用 `v-if` 来处理该组件自身的状态变化（例如，列表为空时的提示）。但**严格禁止**使用 `v-if` 来区分不同的业务组件类型（如 `v-if="card.assetType === '股票'"`）。
    - **禁止项**: 严格禁止使用**计算属性 (`computed`)、方法 (`methods`)、生命周期钩子 (`mounted` 等)、`v-show`、`v-html`** 以及自定义 Vue 组件。此举旨在确保重构后的文件依然是纯粹的、自包含的静态 HTML，避免引入真实的应用级复杂性。

4.  **数据与视图分离 (Data and View Separation)**
    - **明确数据边界**: 为确保数据驱动的正确性，必须严格区分**视图层静态标签**与**动态业务数据**。
    - **视图层静态标签 (Static UI Labels)**: 所有在应用生命周期内其**值本身不会改变**的 UI 文本，都必须在 HTML 模板中硬编码。这包括：
      - **卡片标题**: 如 "股票"、"基金"。
      - **字段标签**: 如 "持仓总值"、"累计市值变动"、"名称/代码"。
    - **动态业务数据 (Dynamic Business Data)**: 所有其**值来源于服务端 API** 或需要在运行时发生变化的数据，**无论其在页面上出现几次**，都必须定义在 Vue 的 `data` 对象中。这包括：
      - **总览数据**: 如 "总资产: 888,888,888.00"、"今日收益: +6,891.24"。
      - **卡片级汇总**: 如单个资产（股票、基金等）的 "持仓总值" 和 "累计市值变动"。
      - **列表项数据**: 用于 `v-for` 渲染的数组及其中的所有业务字段（如股票名称、市值、代码等）。
    - **数据纯净性原则 (Data Purity Principle)**:
      - **数据必须原始**: 所有存储在 Vue `data` 对象中的业务数据，必须是其最原始、最完整的形态。
      - **严禁混入表现层逻辑**: 严禁在数据中硬编码任何纯粹用于视觉表现的符号或格式。最典型的错误就是将用于文本截断的省略号（`...`）直接写入字符串数据中。
      - **关注点分离**: 文本截断等视觉表现问题，**必须**通过 CSS 的 `text-overflow: ellipsis;` 等属性在表现层解决，以确保数据模型的纯净和可复用性。
    - **反例 (Anti-Pattern)**: 为加深理解，以下是**错误**示范。
      ```javascript
      // 错误：将静态卡片标题 "股票" 硬编码在数据模型中
      Vue.createApp({
        data() {
          return {
            stock: {
              title: "股票", // 这是错误的！应在 HTML 的 <span class="card-title"> 中硬编码
              summary: {
                /* ... */
              },
              holdings: [
                /* ... */
              ],
            },
            // ...
          };
        },
      });
      ```
    - **循环中的数据结构**: 当使用 `v-for` 渲染一个项目列表时，其数据源必须是结构清晰、语义明确的纯数据对象数组。严禁将用于视图层展示的**静态标签文本**（如将 "市值/数量" 作为数据传入）混入业务数据中。

5.  **内容与结构**
    - **严禁实现系统 UI**: 在代码中禁止出现任何硬编码的移动设备系统 UI 元素，例如顶部的状态栏（时间、Wi-Fi、电池）和底部的系统导航指示器（Home Indicator）等。
    - **禁止语义化标签**: 为保持与原始代码结构的一致性，并避免引入不必要的复杂性，本次重构**严禁**使用 HTML5 语义化标签（如 `<header>`, `<nav>`, `<main>`, `<section>`, `<article>`, `<footer>` 等）。所有结构都必须使用 `<div>` 标签来构建。
    - **功能分析前置原则 (Functional Analysis First)**: 在动手修改任何代码之前，**必须**首先对原始页面中的每一个 UI 元素（特别是图标和按钮）进行功能性分析，并理解其与相邻元素的逻辑关系。严禁在未完全理解其业务上下文的情况下，仅凭视觉猜测就对其进行重构或归类。

## 2. 重构实施策略

### 策略一：使用 Flexbox 现代化布局

这是提升代码健壮性的关键。我们将用 Flexbox 的 `justify-content` 和 `gap` 属性，来替代僵硬的 `margin-left` 布局。

**实施步骤：**

1.  定位使用 `margin-left` 或 `margin-top` 来进行元素定位的父容器。
2.  移除子元素上的 `margin` 属性。
3.  在父容器上应用 `display: flex` 和 `justify-content`（如 `space-between`）来控制主轴对齐。
4.  对于需要精确间距的元素组，使用一个无样式的 `div` 进行包裹，并应用 `gap` 属性来控制它们之间的距离，从而精确还原视觉。
5.  **微观布局复现 (Micro-Layout Replication)**: 当重构一个包含多个子元素的复杂UI控件（如包含文本和图标的按钮）时，**必须**同样关注其**内部布局**的精确复现。必须使用灵活的内部分布技术（如 `justify-content: space-between`）来替代固定的 `gap` 或 `margin`，以防止在固定宽度的容器中发生意外的换行。

### 策略二：精简 HTML 结构与合并 CSS

这是提升代码可读性和可维护性的关键。

**实施步骤：**

1.  识别并合并样式完全相同的 CSS 类，减少代码重复。例如，如果多个组件都用到了某种内边距或外边距的样式，可以将其抽象成一个通用的工具类。
2.  在确保布局和样式不变的前提下，移除没有实际作用的、纯粹用于包裹的 `<div>` 标签，降低 HTML 的嵌套深度。

### 策略三：实现响应式与自适应布局

这是确保页面在手机、平板、折叠屏乃至桌面等不同尺寸设备上都能提供优秀浏览体验的关键。我们将采用移动端优先的策略，并结合流式布局，实现真正的自适应。

**核心原则：**

1.  **移动端优先 (Mobile-First)**: 所有基础样式都针对移动设备进行设计，并以 375px 视口作为视觉效果的**校准基准**。
2.  **流式布局 (Fluid Layout)**:
    - **水平方向 (Horizontal)**: **必须实现完全流式布局**。严禁在页面级容器（如 `#app`, `body`）上使用任何形式的 `width` 或 `max-width` 限制。所有组件和元素的横向尺寸都必须通过 Flexbox（`flex-grow`, `flex-shrink`）、Grid（`fr` 单位）或百分比等流式技术来定义，确保内容能自然地填充可用空间。**为避免盒模型问题并遵循最佳实践，严禁使用 `width: 100%` 来拉伸 Flexbox 或 Grid 布局中的子项；必须改用 `flex: 1` 或 `fr` 单位。**
    - **375px 校准实践**: 在 375px 视口下进行开发和视觉比对，以确保所有 CSS 规则（字号、内边距、外边距、`gap`）的 `px` 值能够精准还原设计稿。这些 `px` 值定义了元素的微观尺寸和间距，而宏观的布局和拉伸则完全交给流式布局机制处理。
    - **垂直方向 (Vertical)**: 允许使用固定的 `px` 值来定义元素的高度（`height`）和垂直间距（如 `margin-top`, `gap`），以确保在不同设备上保持统一、稳定的垂直节奏和视觉体验。

### 策略四：引入 Vue.js 简化重复结构

这是在**尊重业务边界**的前提下，根除组件内部代码冗余、提升长期可维护性的策略。

**实施步骤：**

1.  在 `head` 标签中，通过 CDN 引入 `Vue`：`<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>`。
2.  **基于已拆分的业务组件**，识别出组件内部的重复性 DOM 结构（如持仓列表）。
3.  **为每个组件抽象其渲染所需的数据**：为每个业务组件创建独立的、结构清晰的根数据对象。在该对象内部，使用嵌套结构（如 `summary` 和 `holdings`）来分别管理其**摘要信息**和**列表数据**。
4.  在脚本中，创建一个 Vue 应用实例，并在其 `data` 对象中管理这些**按业务领域组织好**的数据。
    ```javascript
    Vue.createApp({
      data() {
        return {
          // 股票业务组件数据
          stock: {
            // 'summary' 对象仅在业务组件真实包含"摘要"区域时才需要定义
            summary: {
              totalValue: '8,653,240.44',
              totalChange: '+2,326,918.22'
            },
            // 使用能准确反映业务含义的名称，如 watchlist, favorites, items 等
            watchlist: [
              { name: '腾讯控股', code: '00700', ... },
              { name: '比亚迪股份', code: '01211', ... }
            ]
          },
          // 基金业务组件数据
          fund: {
            // 此处无 'summary'，因为设计稿中的基金列表没有摘要区域
            watchlist: [
              { name: '贝莱德世界科技基金A2', code: 'LU0128526570', ... },
              // ... more funds
            ]
          },
          // ... 其他业务组件的数据
        };
      },
    }).mount("#app");
    ```
5.  在各个业务组件的 HTML 模板中，使用文本插值和 `v-for` 指令来分别渲染其摘要和列表数据。此过程需严格遵守 **[1.5. 编码规范与禁止项](#15-编码规范与禁止项-coding-standards--prohibitions)** 中关于 Vue.js 使用的规定。
6.  此方法将大幅度减少各个业务组件内部手写的 HTML 代码量，并且当未来需要修改列表项时，仅需维护 JavaScript 中对应的独立数据对象即可。

### 策略五：移除系统 UI 元素

这是确保组件纯粹性和可复用性的关键。我们将识别并删除代码中硬编码的系统 UI 元素，避免将系统级界面元素与业务组件混合。

**实施步骤：**

1.  **识别系统 UI 元素**: 在代码中查找状态栏、时间显示、Wi-Fi 信号、电池电量、网络信号图标及各类系统指示器（Home Indicator）等元素。
2.  **直接删除**: **无差别移除**所有识别出的系统级 UI 元素的 HTML 代码和相关 CSS 样式。
3.  **验证纯粹性**: 确保删除后的组件专注于业务逻辑，不包含任何与设备系统相关的视觉元素。

### 策略六：图形占位符原则 (Chart Placeholder Principle)

这是为确保重构工作聚焦于结构与数据，而非在静态文件中进行复杂的视觉渲染。

**核心原则:**

对于所有图表类组件（如折线图、柱状图、饼图、K线图等），应统一使用**占位符**进行替换。

**实施步骤：**

1.  **识别图表**: 定位原始代码中用于渲染图表的 HTML 结构。
2.  **替换为占位符**: 移除其复杂的内部 DOM 结构，将其替换为一个单一的 `<div>` 元素。
3.  **添加描述性文案**: 在该 `<div>` 内部，必须包含清晰的文本，用以说明该占位符代表的图表类型。
    - **示例**: `<div>净值走势图区域</div>`
4.  **应用占位样式 (可选)**: 可以为该 `<div>` 添加简单的背景色或边框，使其在页面布局中清晰可见。
5.  **目的**: 此策略将复杂的图形渲染工作推迟到后续的 JavaScript 开发阶段，使当前的 HTML 重构可以专注于更核心的页面结构、响应式布局和数据模型，避免在静态文件中投入不必要的渲染成本。
