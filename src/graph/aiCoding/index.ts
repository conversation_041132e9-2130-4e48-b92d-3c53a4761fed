import { StateGraph, END, START, Annotation } from "@langchain/langgraph";
import { SystemMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import type { BaseMessage } from "@langchain/core/messages";
import { createModel } from "../designToCode/model/index.js";
import { ToolNode } from "@langchain/langgraph/prebuilt";
import { writeFileTool } from "../../tools/writeFiles.js";

// AI编码状态定义
interface AICodingState {
  messages: BaseMessage[];
  input: string;
  output: string;
  files: any[];
}

// 简化的状态对象定义
const AICodingStateObj = {
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  },
  input: {
    value: (x: string, y: string) => y || x,
    default: () => "",
  },
  output: {
    value: (x: string, y: string) => y || x,
    default: () => "",
  },
  files: {
    value: (x: any[], y: any[]) => y || x,
    default: () => [],
  },
};

// 定义工具
const tools = [writeFileTool];

/**
 * AI编码助手节点
 */
async function aiCodingNode(state: AICodingState) {
  console.log("AICoding: 处理用户消息...", {
    messagesCount: state.messages.length,
    input: state.input?.substring(0, 100) + (state.input?.length > 100 ? '...' : ''),
    filesCount: state.files?.length || 0
  });

  // 创建模型并绑定工具
  const model = createModel("openai").bindTools(tools);

  // 构建消息列表 - 使用现有的消息历史
  let messages = [...state.messages];

  // 如果没有消息历史，添加系统消息
  if (messages.length === 0) {
    const systemPrompt = `你是一个专业的AI编程助手，具有以下能力：

1. **代码编写与优化**：
   - 支持多种编程语言（Python, JavaScript, TypeScript, Java, C++, Go等）
   - 编写高质量、可维护的代码
   - 提供代码优化建议和最佳实践

2. **代码分析与调试**：
   - 分析用户上传的代码文件
   - 识别潜在的bug和性能问题
   - 提供调试建议和解决方案

3. **文档与解释**：
   - 为代码添加详细注释
   - 解释复杂的算法和数据结构
   - 生成技术文档

4. **工具使用**：
   - 可以使用writeFileTool工具将生成的代码保存到文件
   - 支持创建完整的项目结构

请根据用户的需求提供专业的编程帮助。如果用户上传了文件，请仔细分析文件内容并提供相应的建议。`;

    messages.push(new SystemMessage(systemPrompt));
  }

  // 如果有额外的输入，添加为用户消息
  if (state.input && state.input.trim()) {
    messages.push(new HumanMessage(state.input));
  }

  console.log(`AICoding: 调用模型处理 ${messages.length} 条消息`);

  try {
    const responseMessage = await model.invoke(messages);

    console.log("AICoding: 模型响应成功", {
      responseLength: responseMessage.content?.toString().length || 0,
      hasToolCalls: responseMessage.tool_calls?.length > 0
    });

    return {
      messages: [responseMessage],
      output: responseMessage.content?.toString() || "",
    };
  } catch (error) {
    console.error("AICoding: 模型调用失败:", error);

    const errorResponse = new AIMessage("抱歉，我遇到了一些问题，请稍后再试。");

    return {
      messages: [responseMessage],
      output: errorResponse.content as string,
    };
  }
}

/**
 * 工具调用节点
 */
const toolNode = new ToolNode(tools);

/**
 * 判断是否需要调用工具
 */
function shouldCallTools(state: AICodingState) {
  const lastMessage = state.messages[state.messages.length - 1];
  
  // 检查最后一条消息是否包含工具调用
  if (lastMessage && 'tool_calls' in lastMessage && lastMessage.tool_calls && lastMessage.tool_calls.length > 0) {
    console.log("AICoding: 检测到工具调用，转向工具节点");
    return "tools";
  }
  
  console.log("AICoding: 无工具调用，结束流程");
  return END;
}

/**
 * 处理工具调用后的响应
 */
async function handleToolResponse(state: AICodingState) {
  console.log("AICoding: 处理工具调用结果...");
  
  // 获取工具调用的结果
  const lastMessage = state.messages[state.messages.length - 1];
  let toolResult = "";
  
  if (lastMessage && lastMessage.content) {
    toolResult = lastMessage.content.toString();
  }

  // 创建一个总结响应
  const summaryMessage = new AIMessage(
    `工具执行完成。${toolResult}`
  );

  return {
    messages: [summaryMessage],
    output: summaryMessage.content as string,
  };
}

// 构建AI编码工作流图
const workflow = new StateGraph<AICodingState>({
  channels: AICodingStateObj,
})
  .addNode("coding", aiCodingNode)
  .addNode("tools", toolNode)
  .addNode("handleToolResponse", handleToolResponse)
  .addEdge(START, "coding")
  .addConditionalEdges("coding", shouldCallTools)
  .addEdge("tools", "handleToolResponse")
  .addEdge("handleToolResponse", END);

// 编译工作流
export const graph = workflow.compile();
graph.name = "aiCoding";

console.log("AICoding: 图已创建并编译完成");
