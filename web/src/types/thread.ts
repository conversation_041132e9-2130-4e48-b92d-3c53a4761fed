import { Thread } from "@langchain/langgraph-sdk";

/**
 * 线程排序字段类型
 */
export type ThreadSortBy = "thread_id" | "status" | "created_at" | "updated_at";

/**
 * 排序顺序类型
 */
export type SortOrder = "asc" | "desc";

/**
 * 设计到代码图状态
 */
export interface DesignToCodeState {
  messages: any[];
  error?: string;
  input: any[];
  output: string;
  htmlResults: any[];
  combinedHtml?: string;
  projectCode?: string;
}

/**
 * 简单聊天图状态
 */
export interface SimpleChatState {
  messages: any[];
  error?: string;
  input: string;
  output: string;
}

/**
 * 联合图状态类型
 */
export type AnyGraphState = DesignToCodeState | SimpleChatState;

/**
 * 分页选项
 */
export interface PaginationOptions {
  /**
   * 最大返回线程数
   * @default 25
   */
  limit?: number;
  /**
   * 偏移量
   * @default 0
   */
  offset?: number;
  /**
   * 排序字段
   * @default "updated_at"
   */
  sortBy?: ThreadSortBy;
  /**
   * 排序顺序
   * @default "desc"
   */
  sortOrder?: SortOrder;
}

/**
 * 线程元数据
 */
export interface ThreadMetadata {
  thread_id: string;
  graph_id?: string;
  created_at: string;
  updated_at: string;
  status?: string;
  title?: string;
  description?: string;
  user_id?: string;
  [key: string]: any;
}

/**
 * 扩展的线程类型，包含元数据
 */
export interface ExtendedThread<T extends AnyGraphState = AnyGraphState> extends Thread<T> {
  metadata: ThreadMetadata;
}
