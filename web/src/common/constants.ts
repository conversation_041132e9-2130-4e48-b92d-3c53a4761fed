import { Bo<PERSON>, MessageSquare } from "lucide-react";

export const DEFAULT_AGENT = "simpleChat";

// 定义可用的agent类型
export const AVAILABLE_AGENTS = [
  {
    id: "simpleChat",
    name: "Simple Agent",
    description: "简单的聊天助手，适合日常对话和问答",
    icon: MessageSquare,
  },
  {
    id: "designToCode",
    name: "Design to Code Agent",
    description: "设计稿转代码助手，将设计稿转换为可用的代码",
    icon: Bot,
  },
] as const;

// Agent信息获取函数
export const getAgentInfo = (agentId: string) => {
  const agent = AVAILABLE_AGENTS.find(a => a.id === agentId);
  return agent || {
    id: agentId,
    name: agentId,
    description: "未知助手类型",
    icon: Bot
  };
};
