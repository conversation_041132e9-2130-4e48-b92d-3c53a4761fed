/**
 * SWR配置常量
 */
export const THREAD_SWR_CONFIG = {
  // 刷新间隔（毫秒）
  refreshInterval: 30000, // 30秒
  // 窗口聚焦时重新验证
  revalidateOnFocus: true,
  // 重新连接时重新验证
  revalidateOnReconnect: true,
  // 错误重试次数
  errorRetryCount: 3,
  // 错误重试间隔（毫秒）
  errorRetryInterval: 5000, // 5秒
  // 去重间隔（毫秒）
  dedupingInterval: 2000, // 2秒
};

/**
 * 默认分页配置
 */
export const DEFAULT_PAGINATION = {
  limit: 25,
  offset: 0,
  sortBy: "updated_at" as const,
  sortOrder: "desc" as const,
};
