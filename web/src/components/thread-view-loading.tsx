"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ArrowLeft, Loader2 } from "lucide-react";

interface ThreadViewLoadingProps {
  onBackToHome: () => void;
}

export function ThreadViewLoading({ onBackToHome }: ThreadViewLoadingProps) {
  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
          </div>
          <h2 className="text-xl font-semibold">加载线程数据</h2>
          <p className="text-sm text-muted-foreground">
            正在获取聊天记录，请稍候...
          </p>
        </CardHeader>
        <CardContent>
          <Button onClick={onBackToHome} className="w-full" variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回首页
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
