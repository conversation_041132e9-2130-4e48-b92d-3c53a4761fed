"use client";

import React from "react";
// import { Base64ContentBlock } from "@langchain/core/messages";
import { Base64ContentBlock } from "@/types/content-block";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

interface ContentBlocksPreviewProps {
  blocks: Base64ContentBlock[];
  onRemove: (id: string) => void;
  className?: string;
}

export function ContentBlocksPreview({
  blocks,
  onRemove,
  className,
}: ContentBlocksPreviewProps) {
  if (blocks.length === 0) {
    return null;
  }

  return (
    <div className={cn("mb-3 space-y-2", className)}>
      {blocks.map((block) => (
        <div
          key={block.id}
          className="relative inline-block mr-2 mb-2 group"
        >
          {block.type === "image_url" && block.image_url?.url && (
            <div className="relative">
              <img
                src={block.image_url.url}
                alt="Uploaded content"
                className="max-w-xs max-h-32 rounded-md border border-border object-cover"
              />
              <Button
                variant="destructive"
                size="icon"
                className="absolute -top-2 -right-2 h-6 w-6 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => onRemove(block.id!)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
