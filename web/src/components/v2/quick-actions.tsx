"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Code, 
  FileText, 
  Bug, 
  Lightbulb, 
  RefreshCw, 
  Search,
  Zap
} from "lucide-react";

interface QuickActionsProps {
  setQuickActionPrompt: (prompt: string) => void;
}

const quickActions = [
  {
    id: "code-review",
    title: "代码审查",
    description: "审查代码质量和最佳实践",
    icon: Code,
    prompt: "请帮我审查这段代码，检查是否有潜在的问题、性能优化点或最佳实践建议。",
  },
  {
    id: "bug-fix",
    title: "修复Bug",
    description: "分析和修复代码中的错误",
    icon: Bug,
    prompt: "我遇到了一个bug，请帮我分析问题原因并提供修复方案。",
  },
  {
    id: "documentation",
    title: "生成文档",
    description: "为代码生成详细文档",
    icon: FileText,
    prompt: "请为这段代码生成详细的文档，包括函数说明、参数描述和使用示例。",
  },
  {
    id: "optimization",
    title: "性能优化",
    description: "优化代码性能和效率",
    icon: Zap,
    prompt: "请分析这段代码的性能，并提供优化建议来提高执行效率。",
  },
  {
    id: "refactor",
    title: "重构代码",
    description: "改进代码结构和可读性",
    icon: RefreshCw,
    prompt: "请帮我重构这段代码，提高其可读性、可维护性和代码质量。",
  },
  {
    id: "explain",
    title: "代码解释",
    description: "解释代码的工作原理",
    icon: Search,
    prompt: "请详细解释这段代码的工作原理，包括每个部分的作用和整体逻辑。",
  },
  {
    id: "feature",
    title: "添加功能",
    description: "为现有代码添加新功能",
    icon: Lightbulb,
    prompt: "我想为这个项目添加新功能，请帮我设计实现方案。",
  },
];

export function QuickActions({ setQuickActionPrompt }: QuickActionsProps) {
  const handleQuickAction = (prompt: string) => {
    setQuickActionPrompt(prompt);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base font-semibold flex items-center gap-2">
          <Zap className="h-4 w-4" />
          快速操作
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {quickActions.map((action) => (
            <Button
              key={action.id}
              variant="outline"
              className="h-auto p-3 flex flex-col items-start gap-2 text-left hover:bg-accent"
              onClick={() => handleQuickAction(action.prompt)}
            >
              <div className="flex items-center gap-2 w-full">
                <action.icon className="h-4 w-4 text-primary" />
                <span className="font-medium text-sm">{action.title}</span>
              </div>
              <span className="text-xs text-muted-foreground">
                {action.description}
              </span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
