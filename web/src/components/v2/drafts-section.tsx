"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText, Trash2, Clock } from "lucide-react";
import { toast } from "sonner";

interface Draft {
  id: string;
  content: string;
  timestamp: string;
  preview: string;
}

interface DraftsSectionProps {
  onLoadDraft: (content: string) => void;
}

const DRAFTS_STORAGE_KEY = "langgraph-mvp-drafts";
const MAX_DRAFTS = 5;

export function DraftsSection({ onLoadDraft }: DraftsSectionProps) {
  const [drafts, setDrafts] = useState<Draft[]>([]);

  // 加载草稿列表
  useEffect(() => {
    try {
      const savedDrafts = localStorage.getItem(DRAFTS_STORAGE_KEY);
      if (savedDrafts) {
        setDrafts(JSON.parse(savedDrafts));
      }
    } catch (error) {
      console.error("Failed to load drafts:", error);
    }
  }, []);

  // 保存草稿列表
  const saveDrafts = (newDrafts: Draft[]) => {
    try {
      localStorage.setItem(DRAFTS_STORAGE_KEY, JSON.stringify(newDrafts));
      setDrafts(newDrafts);
    } catch (error) {
      console.error("Failed to save drafts:", error);
      toast.error("保存草稿失败");
    }
  };

  // 删除草稿
  const deleteDraft = (id: string) => {
    const newDrafts = drafts.filter(draft => draft.id !== id);
    saveDrafts(newDrafts);
    toast.success("草稿已删除");
  };

  // 加载草稿
  const loadDraft = (content: string) => {
    onLoadDraft(content);
    toast.success("草稿已加载");
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return "刚刚";
    if (diffMins < 60) return `${diffMins}分钟前`;
    if (diffHours < 24) return `${diffHours}小时前`;
    if (diffDays < 7) return `${diffDays}天前`;
    return date.toLocaleDateString("zh-CN");
  };

  if (drafts.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-base font-semibold flex items-center gap-2">
          <FileText className="h-4 w-4" />
          草稿箱
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {drafts.map((draft) => (
            <div
              key={draft.id}
              className="flex items-start gap-3 p-3 border border-border rounded-md hover:bg-accent/50 transition-colors"
            >
              <div className="flex-1 min-w-0">
                <p className="text-sm text-foreground line-clamp-2 mb-1">
                  {draft.preview}
                </p>
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  {formatTime(draft.timestamp)}
                </div>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2"
                  onClick={() => loadDraft(draft.content)}
                >
                  加载
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 px-2 text-destructive hover:text-destructive"
                  onClick={() => deleteDraft(draft.id)}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
