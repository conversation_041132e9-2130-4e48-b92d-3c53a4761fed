"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { v4 as uuidv4 } from "uuid";
import { toast } from "sonner";
import { Client, Thread } from "@langchain/langgraph-sdk";

import { getAgentInfo, DEFAULT_AGENT } from "@/common/constants";
import { inputDesignItems } from "@/common/mock";

import { ChatHeader } from "./chat/chat-header";
import { MessageList } from "./chat/message-list";
import { ChatInput } from "./chat/chat-input";
import { ChatSidebarPanel } from "./chat/chat-sidebar-panel";

// 获取初始线程数据的函数
async function fetchInitialThread(
  client: Client,
  threadId: string,
  reqCount = 0,
): Promise<Thread | null> {
  try {
    return await client.threads.get(threadId);
  } catch (e) {
    console.error("Failed to fetch thread", {
      requestCount: reqCount,
      error: e,
    });
    // 重试最多 5 次
    if (reqCount < 5) {
      return fetchInitialThread(client, threadId, reqCount + 1);
    }
    return null;
  }
}

interface ChatInterfaceProps {
  onBack: () => void;
  threadId?: string;
  selectedAgent?: string;
  stream?: any; // 简化类型定义
  initialThread?: any;
  displayThread?: any; // 添加显示线程元数据
}

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
}

// 从 LangGraph 消息中提取内容的函数
function getMessageContentString(content: any): string {
  if (typeof content === 'string') {
    return content;
  }
  if (Array.isArray(content)) {
    return content.map(item => {
      if (typeof item === 'string') return item;
      if (item.text) return item.text;
      return JSON.stringify(item);
    }).join('');
  }
  if (content && typeof content === 'object' && content.text) {
    return content.text;
  }
  return JSON.stringify(content);
}

export function ChatInterface({
  onBack,
  threadId: propThreadId,
  selectedAgent = DEFAULT_AGENT,
  stream,
  initialThread,
  displayThread
}: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState("");
  const threadId = propThreadId || uuidv4();
  const [isLoading, setIsLoading] = useState(false);
  const [initialFetchedThread, setInitialFetchedThread] = useState<Thread | null>(null);
  const initialThreadFetched = useRef(false);

  // 使用 stream 的 loading 状态，如果可用的话
  const actualIsLoading = stream?.isLoading ?? isLoading;

  // 创建 dummy thread 数据
  const dummyThread = {
    thread_id: threadId,
    values: {
      messages: [],
      input: [],
    },
    status: "idle" as const,
    updated_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    metadata: {
      graph_id: selectedAgent,
      assistant_id: selectedAgent,
    },
    config: {},
  };

  // 从 stream 消息转换为本地消息格式
  const convertStreamMessagesToLocal = useCallback((streamMessages: any[]): Message[] => {
    return streamMessages.map((msg: any) => ({
      id: msg.id || uuidv4(),
      role: msg.type === 'human' ? 'user' : 'assistant',
      content: getMessageContentString(msg.content),
      timestamp: new Date(msg.created_at || Date.now()),
    }));
  }, []);

  // 使用 LangGraph SDK 获取线程历史
  useEffect(() => {
    // 只有当没有获取过初始线程且 stream.client 可用时才获取
    if (!initialThreadFetched.current && stream?.client) {
      fetchInitialThread(stream.client, threadId)
        .then((thread) => {
          setInitialFetchedThread(thread);
          if (thread?.values && (thread.values as any)?.messages && Array.isArray((thread.values as any).messages)) {
            // 转换线程中的历史消息
            const convertedMessages = (thread.values as any).messages.map((msg: any) => ({
              id: msg.id || uuidv4(),
              role: msg.type === 'human' ? 'user' : 'assistant' as 'user' | 'assistant',
              content: getMessageContentString(msg.content),
              timestamp: new Date(msg.created_at || Date.now()),
            }));
            setMessages(convertedMessages);
          }
        })
        .catch((err) => {
          console.error("Failed to fetch initial thread:", err);
        })
        .finally(() => {
          initialThreadFetched.current = true;
        });
    }

    // 如果已经获取过且有新的线程数据，清理状态
    if (initialThreadFetched.current && initialFetchedThread) {
      // 这里可以根据需要决定是否清理
    }
  }, [threadId]);

  // 合并 stream 消息
  useEffect(() => {
    if (stream?.messages && stream.messages.length > 0) {
      const convertedMessages = convertStreamMessagesToLocal(stream.messages);
      setMessages(convertedMessages);
    }
    // 注意：不要从 localStorage 加载历史消息
  }, [stream?.messages, convertStreamMessagesToLocal]);

  // 注意：消息现在通过 API 自动保存，不需要手动保存到 localStorage

  const handleSendMessage = async () => {
    if (!input.trim() || actualIsLoading) return;
    const currentInput = selectedAgent === "designToCode" && messages.length === 0 ? inputDesignItems.input : input.trim();

    // 如果有 stream，使用新的发送方式
    if (stream && stream.submit) {
      const newHumanMessage = {
        id: uuidv4(),
        content: input.trim(),
        type: "human" as const,
      };

      stream.submit(
        {
          input: currentInput,
          messages: [newHumanMessage],
        },
        {
          streamResumable: true,
        },
      );

      setInput("");
      return;
    }

    // 简化的回退实现
    const userMessage: Message = {
      id: uuidv4(),
      role: "user",
      content: input.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInput("");
    setIsLoading(true);

    try {
      // 这里可以添加简化的 API 调用逻辑
      toast.success("消息已发送");
    } catch (error: any) {
      toast.error("发送消息失败", {
        description: error?.message || "请检查网络连接和服务状态",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleStopStream = () => {
    // 如果有 stream，使用 stream 的 stop 方法
    if (stream && stream.stop) {
      stream.stop();
    }
  };

  // 不需要加载界面，直接显示聊天界面

  return (
    <div className="bg-background flex h-screen flex-1 flex-col">
      {/* Header */}
      <ChatHeader
        title={displayThread?.title || getAgentInfo(selectedAgent).name}
        threadId={threadId}
        status={actualIsLoading ? "running" : (displayThread?.status || "completed")}
        repository={displayThread?.repository || dummyThread?.metadata?.graph_id}
        onBack={onBack}
      />

      {/* Main Content - Split Layout */}
      <div className="flex w-full pt-12">
        {/* Left Side - Chat */}
        <div className="border-border bg-muted/30 flex h-full w-1/3 flex-col overflow-hidden border-r">
          <MessageList
            messages={messages}
            isLoading={actualIsLoading}
            emptyStateTitle={`开始与 ${getAgentInfo(selectedAgent).name} 对话`}
            emptyStateDescription={getAgentInfo(selectedAgent).description}
          />
          <ChatInput
            value={input}
            onChange={setInput}
            onSend={handleSendMessage}
            onStop={handleStopStream}
            isLoading={actualIsLoading}
            placeholder={
              selectedAgent === "designToCode"
                ? "请输入HTML设计稿内容或描述您的设计需求..."
                : "输入您的消息..."
            }
          />
        </div>

        {/* Right Side - Thread Info & Debug */}
        <ChatSidebarPanel
          threadData={dummyThread}
          displayThread={displayThread}
          isLoading={false}
        />
      </div>
    </div>
  );
}
