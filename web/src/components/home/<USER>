import { Card, CardContent } from "@/components/ui/card";
import { TerminalInput } from "@/components/terminal-input";
import { ContentBlocksPreview } from "@/components/thread/ContentBlocksPreview";
import { DEFAULT_API_URL } from "@/providers/client";
import { cn } from "@/lib/utils";
import { Base64ContentBlock } from "@/types/content-block";
import { Dispatch, SetStateAction } from "react";

interface ChatInputSectionProps {
  selectedAgent: string;
  contentBlocks: Base64ContentBlock[];
  setContentBlocks: Dispatch<SetStateAction<Base64ContentBlock[]>>;
  dropRef: React.RefObject<HTMLDivElement | null>;
  dragOver: boolean;
  handlePaste: (e: React.ClipboardEvent<HTMLTextAreaElement>) => void;
  removeBlock: (id: string) => void;
  quickActionPrompt: string;
  setQuickActionPrompt: Dispatch<SetStateAction<string>>;
  draftToLoad: string;
  autoAccept: boolean;
  setAutoAccept: Dispatch<SetStateAction<boolean>>;
}

export function ChatInputSection({
  selectedAgent,
  contentBlocks,
  setContentBlocks,
  dropRef,
  dragOver,
  handlePaste,
  removeBlock,
  quickActionPrompt,
  setQuickActionPrompt,
  draftToLoad,
  autoAccept,
  setAutoAccept,
}: ChatInputSectionProps) {
  return (
    <Card
      className={cn(
        "border-border bg-card py-0",
        dragOver
          ? "border-primary border-2 border-dotted"
          : "border border-solid",
      )}
      ref={dropRef}
    >
      <CardContent className="p-4">
        <ContentBlocksPreview
          blocks={contentBlocks}
          onRemove={removeBlock}
        />
        <div className="space-y-3">
          <TerminalInput
            placeholder="描述您的编程任务或提出问题..."
            apiUrl={DEFAULT_API_URL}
            assistantId={selectedAgent}
            selectedAgent={selectedAgent}
            contentBlocks={contentBlocks}
            setContentBlocks={setContentBlocks}
            onPaste={handlePaste}
            quickActionPrompt={quickActionPrompt}
            setQuickActionPrompt={setQuickActionPrompt}
            draftToLoad={draftToLoad}
            autoAcceptPlan={autoAccept}
            setAutoAcceptPlan={setAutoAccept}
          />
        </div>
      </CardContent>
    </Card>
  );
}
