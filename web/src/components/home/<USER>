import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Settings } from "lucide-react";
import { AVAILABLE_AGENTS } from "@/common/constants";

interface AgentSelectorProps {
  selectedAgent: string;
  onAgentChange: (agent: string) => void;
}

export function AgentSelector({ selectedAgent, onAgentChange }: AgentSelectorProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-center flex items-center justify-center gap-2">
          <Settings className="h-5 w-5" />
          选择智能助手
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">助手类型</label>
          <Select value={selectedAgent} onValueChange={onAgentChange}>
            <SelectTrigger>
              <SelectValue placeholder="选择一个助手" />
            </SelectTrigger>
            <SelectContent>
              {AVAILABLE_AGENTS.map((agent) => (
                <SelectItem key={agent.id} value={agent.id}>
                  <div className="flex items-center gap-2">
                    <agent.icon className="h-4 w-4" />
                    {agent.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* 显示选中agent的描述 */}
        {selectedAgent && (
          <div className="p-3 bg-muted rounded-md">
            <p className="text-sm text-muted-foreground">
              {AVAILABLE_AGENTS.find(agent => agent.id === selectedAgent)?.description}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
