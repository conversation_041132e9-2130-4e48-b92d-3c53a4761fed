import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, Archive } from "lucide-react";
import { ThreadCard } from "./ThreadCard";
import { ThreadCardLoading } from "./ThreadCardLoading";

interface RecentThreadsProps {
  threads: any[];
  threadsLoading: boolean;
  onThreadClick: (threadId: string) => void;
  onViewAllThreads: () => void;
}

export function RecentThreads({
  threads,
  threadsLoading,
  onThreadClick,
  onViewAllThreads,
}: RecentThreadsProps) {
  const displayThreads = threads.slice(0, 4);

  return (
    <div>
      <div className="mb-3 flex items-center justify-between">
        <h2 className="text-foreground text-base font-semibold">
          最近的对话
        </h2>
        <Button
          variant="outline"
          size="sm"
          className="border-border text-muted-foreground hover:text-foreground h-7 text-xs"
          onClick={onViewAllThreads}
        >
          <Plus className="h-3 w-3 mr-1" />
          所有线程
        </Button>
      </div>

      {threadsLoading || threads.length ? (
        <div className="grid gap-3 md:grid-cols-2">
          {threadsLoading && threads.length === 0 && (
            <>
              <Card className="animate-pulse">
                <CardContent className="p-4">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardContent>
              </Card>
              <Card className="animate-pulse">
                <CardContent className="p-4">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardContent>
              </Card>
            </>
          )}
          {displayThreads.map((thread) => (
            <ThreadCard
              key={thread.thread_id}
              thread={thread}
              onClick={() => onThreadClick(thread.thread_id)}
            />
          ))}
        </div>
      ) : (
        <div className="flex items-center justify-center py-8">
          <span className="text-muted-foreground flex items-center gap-2">
            <Archive className="size-4" />
            <span className="text-sm">暂无对话记录</span>
          </span>
        </div>
      )}
    </div>
  );
}
