import { Card, CardContent } from "@/components/ui/card";

interface ThreadCardProps {
  thread: any;
  onClick: () => void;
}

export function ThreadCard({ thread, onClick }: ThreadCardProps) {
  return (
    <Card
      className="cursor-pointer hover:shadow-md transition-shadow border-l-4 border-l-primary"
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-sm truncate">
              {thread.title || `线程 ${thread.thread_id.slice(0, 8)}`}
            </h3>
            <p className="text-xs text-muted-foreground mt-1 font-mono">
              ID: {thread.thread_id}
            </p>
            <div className="mt-2 space-y-1">
              <p className="text-xs text-muted-foreground">
                更新时间: {new Date(thread.updated_at).toLocaleString("zh-CN")}
              </p>
              {thread.status && (
                <div className="flex items-center gap-1">
                  <div className={`h-2 w-2 rounded-full ${
                    thread.status === 'running' ? 'bg-yellow-500' :
                    thread.status === 'completed' ? 'bg-green-500' : 'bg-gray-500'
                  }`} />
                  <span className="text-xs text-muted-foreground capitalize">
                    {thread.status}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
