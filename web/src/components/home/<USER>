import { ThemeToggle } from "@/components/theme-toggle";

export function HomeHeader() {
  return (
    <div className="border-border bg-card border-b px-4 py-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <h1 className="text-xl font-bold text-foreground">
            LangGraph MVP Demo
          </h1>
          <p className="text-muted-foreground text-sm hidden md:block">
            基于LangGraph的智能Agent服务平台
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <span className="text-muted-foreground text-xs">ready</span>
            <div className="h-1 w-1 rounded-full bg-green-500 dark:bg-green-600"></div>
          </div>
          <ThemeToggle />
        </div>
      </div>
    </div>
  );
}
