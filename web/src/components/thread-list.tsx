"use client";

import React from "react";
import { useThreadsSWR } from "@/hooks/useThreadsSWR";
import { threadsToMetadata, formatThreadUpdatedAt, getThreadDisplayTitle } from "@/lib/thread-utils";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, MessageSquare, Clock, User } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface ThreadListProps {
  /**
   * 助手ID（图ID），用于过滤特定图的线程
   */
  assistantId?: string;
  /**
   * 当前用户ID
   */
  currentUserId?: string;
  /**
   * 每页显示的线程数量
   */
  limit?: number;
  /**
   * 点击线程时的回调
   */
  onThreadClick?: (threadId: string) => void;
  /**
   * 是否显示用户信息
   */
  showUserInfo?: boolean;
}

/**
 * 线程列表组件
 * 使用useThreadsSWR hooks获取和显示线程列表
 */
export function ThreadList({
  assistantId,
  currentUserId,
  limit = 10,
  onThreadClick,
  showUserInfo = false,
}: ThreadListProps) {
  const {
    threads,
    error,
    isLoading,
    isValidating,
    mutate,
    hasMore,
  } = useThreadsSWR({
    assistantId,
    currentUserId,
    pagination: {
      limit,
      offset: 0,
      sortBy: "updated_at",
      sortOrder: "desc",
    },
  });

  // 转换为元数据格式以便显示
  const threadMetadata = threadsToMetadata(threads);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-6 w-6 animate-spin" />
        <span className="ml-2">加载线程中...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <p className="text-red-600">加载线程失败: {error.message}</p>
            <Button
              variant="outline"
              size="sm"
              onClick={() => mutate()}
              className="mt-2"
            >
              重试
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (threadMetadata.length === 0) {
    return (
      <div className="p-8 text-center">
        <MessageSquare className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-4 text-lg font-medium text-gray-900">暂无线程</h3>
        <p className="mt-2 text-gray-500">
          {assistantId ? "该助手还没有任何对话线程" : "还没有任何对话线程"}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">
          线程列表 ({threadMetadata.length})
        </h2>
        <div className="flex items-center gap-2">
          {isValidating && (
            <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => mutate()}
            disabled={isValidating}
          >
            刷新
          </Button>
        </div>
      </div>

      <div className="space-y-3">
        {threads.map((thread, index) => (
          <Card key={thread.thread_id} className="border-l-4 border-l-blue-500 cursor-pointer" onClick={() => onThreadClick?.(thread.thread_id)}>
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="font-medium text-sm">
                    线程 #{index + 1}
                  </h3>
                  <p className="text-xs text-gray-500 mt-1 font-mono">
                    ID: {thread.thread_id}
                  </p>
                  <div className="mt-2 space-y-1">
                    <p className="text-xs text-gray-600">
                      创建时间: {new Date(thread.created_at).toLocaleString("zh-CN")}
                    </p>
                    <p className="text-xs text-gray-600">
                      更新时间: {new Date(thread.updated_at).toLocaleString("zh-CN")}
                    </p>
                    {thread.metadata?.graph_id ? (
                      <p className="text-xs text-gray-600">
                        图: {thread.metadata.graph_id as string}
                      </p>
                    ) : null}
                    {thread.metadata?.user_id ? (
                      <p className="text-xs text-gray-600">
                        用户ID: {thread.metadata.user_id as string}
                      </p>
                    ) : null}
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  <Badge variant={thread.status === "busy" ? "default" : "secondary"}>
                    {thread.status || "idle"}
                  </Badge>
                  {/* {thread.metadata && Object.keys(thread.metadata).length > 0 && (
                    <Badge variant="outline" className="text-xs">
                      有元数据
                    </Badge>
                  )} */}
                </div>
              </div>

              {/* 显示元数据 */}
              {thread.metadata && Object.keys(thread.metadata).length > 0 && (
                <details className="mt-3">
                  <summary className="text-xs text-gray-500 cursor-pointer">
                    查看元数据
                  </summary>
                  <pre className="mt-2 text-xs bg-gray-50 p-2 rounded overflow-auto">
                    {JSON.stringify(thread.metadata, null, 2)}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {hasMore && (
        <div className="text-center">
          <Button variant="outline" size="sm">
            加载更多
          </Button>
        </div>
      )}
    </div>
  );
}
