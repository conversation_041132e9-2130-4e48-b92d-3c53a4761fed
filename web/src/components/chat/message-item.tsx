"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import { cn } from "@/lib/utils";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
}

interface MessageItemProps {
  message: Message;
  userAvatar?: string;
  userName?: string;
}

// 消息复制按钮组件
function MessageCopyButton({ content }: { content: string }) {
  const [copied, setCopied] = useState(false);

  const handleCopy = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.stopPropagation();
    navigator.clipboard.writeText(content);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Button
      onClick={(e) => handleCopy(e)}
      variant="ghost"
      size="sm"
      className="h-6 w-6 p-1 opacity-0 transition-opacity group-hover:opacity-100"
    >
      {copied ? (
        <CopyCheck className="h-3 w-3 text-green-500" />
      ) : (
        <Copy className="h-3 w-3" />
      )}
    </Button>
  );
}

// 加载动画组件
export function LoadingMessageDots() {
  return (
    <div className="flex items-center space-x-1 text-sm text-foreground">
      <div className="flex space-x-1">
        <div className="h-1 w-1 rounded-full bg-current animate-pulse" />
        <div className="h-1 w-1 rounded-full bg-current animate-pulse" style={{ animationDelay: "200ms" }} />
        <div className="h-1 w-1 rounded-full bg-current animate-pulse" style={{ animationDelay: "400ms" }} />
      </div>
    </div>
  );
}

export function MessageItem({ message, userAvatar, userName }: MessageItemProps) {
  return (
    <div className="group bg-muted flex items-start gap-3 rounded-lg p-3">
      <div className="mt-0.5 flex-shrink-0">
        {message.role === "user" ? (
          userAvatar ? (
            <div className="bg-muted flex h-6 w-6 items-center justify-center overflow-hidden rounded-full">
              <img
                src={userAvatar}
                alt={userName || "User"}
                className="h-full w-full object-cover"
              />
            </div>
          ) : (
            <div className="bg-muted flex h-6 w-6 items-center justify-center rounded-full">
              <User className="text-muted-foreground h-4 w-4" />
            </div>
          )
        ) : (
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-950/50">
            <Bot className="size-4 text-blue-700 dark:text-blue-300" />
          </div>
        )}
      </div>
      <div className="relative min-w-0 flex-1 space-y-1 overflow-x-hidden">
        <div className="flex items-center justify-between gap-2">
          <span className="text-muted-foreground text-xs font-medium">
            {message.role === "user" ? (userName || "You") : "Assistant"}
          </span>
          <div className="opacity-0 transition-opacity group-hover:opacity-100">
            <MessageCopyButton content={message.content} />
          </div>
        </div>
        {message.content ? (
          <div className="text-foreground overflow-x-hidden text-sm whitespace-pre-wrap break-words">
            {message.content}
          </div>
        ) : (
          <LoadingMessageDots />
        )}
        <div className="text-muted-foreground text-xs">
          {message.timestamp.toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
}
