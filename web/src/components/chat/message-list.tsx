"use client";

import { useEffect, useRef } from "react";
import { MessageItem } from "./message-item";
import { Bot } from "lucide-react";

interface Message {
  id: string;
  role: "user" | "assistant";
  content: string;
  timestamp: Date;
}

interface MessageListProps {
  messages: Message[];
  isLoading?: boolean;
  emptyStateTitle?: string;
  emptyStateDescription?: string;
  userAvatar?: string;
  userName?: string;
}

export function MessageList({
  messages,
  isLoading = false,
  emptyStateTitle = "开始对话",
  emptyStateDescription = "发送消息开始与助手对话",
  userAvatar,
  userName,
}: MessageListProps) {
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const lastMessageCountRef = useRef(0);

  // 滚动到底部
  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  };

  // 当消息数量变化时滚动到底部
  useEffect(() => {
    if (messages.length !== lastMessageCountRef.current) {
      lastMessageCountRef.current = messages.length;
      setTimeout(() => {
        scrollToBottom();
      }, 0);
    }
  }, [messages.length]);

  return (
    <div className="relative flex-1">
      <div
        ref={scrollAreaRef}
        className="absolute inset-0 overflow-y-auto scrollbar-thin scrollbar-track-transparent scrollbar-thumb-muted-foreground/20"
      >
        <div className="space-y-4 p-4">
          {messages.length === 0 && !isLoading ? (
            <div className="text-center text-muted-foreground py-8">
              <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">{emptyStateTitle}</p>
              <p className="text-sm mt-2">{emptyStateDescription}</p>
            </div>
          ) : (
            messages.map((message) => (
              <MessageItem
                key={message.id}
                message={message}
                userAvatar={userAvatar}
                userName={userName}
              />
            ))
          )}
        </div>
      </div>
    </div>
  );
}
