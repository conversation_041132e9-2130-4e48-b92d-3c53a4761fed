"use client";

import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { ArrowUp, Loader2 } from "lucide-react";

interface ChatInputProps {
  value: string;
  onChange: (value: string) => void;
  onSend: () => void;
  onStop?: () => void;
  isLoading?: boolean;
  placeholder?: string;
  disabled?: boolean;
}

export function ChatInput({
  value,
  onChange,
  onSend,
  onStop,
  isLoading = false,
  placeholder = "Type your message...",
  disabled = false,
}: ChatInputProps) {
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey) && !isLoading) {
      e.preventDefault();
      onSend();
    }
  };

  return (
    <div className="border-border bg-muted/30 border-t p-4">
      <div className="flex gap-2">
        <Textarea
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="border-border bg-background text-foreground placeholder:text-muted-foreground min-h-[60px] flex-1 resize-none text-sm"
          onKeyDown={handleKeyDown}
          disabled={disabled || isLoading}
        />
        {isLoading ? (
          <Button
            className="size-8 rounded-full border border-white/20 transition-all duration-200 hover:border-white/30 disabled:border-transparent"
            variant="destructive"
            onClick={onStop}
            size="icon"
          >
            <Loader2 className="size-4 animate-spin" />
          </Button>
        ) : (
          <Button
            onClick={onSend}
            disabled={!value.trim() || disabled}
            size="icon"
            className="size-8 rounded-full border border-white/20 transition-all duration-200 hover:border-white/30 disabled:border-transparent"
          >
            <ArrowUp className="size-4" />
          </Button>
        )}
      </div>
      <div className="text-muted-foreground mt-2 text-xs">
        Press Cmd+Enter to send
      </div>
    </div>
  );
}
