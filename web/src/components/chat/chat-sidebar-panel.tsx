"use client";

import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Clock, Terminal } from "lucide-react";

interface ThreadData {
  thread_id: string;
  created_at: string;
  updated_at: string;
  metadata: {
    graph_id: string;
    assistant_id: string;
    [key: string]: any;
  };
  status: string;
  config: any;
  values: {
    messages: any[];
    input: any[];
  };
}

interface ChatSidebarPanelProps {
  threadData?: ThreadData | null;
  displayThread?: any; // 线程显示元数据
  isLoading?: boolean;
}

// 线程信息面板
function ThreadInfoPanel({
  threadData,
  displayThread
}: {
  threadData?: ThreadData | null;
  displayThread?: any;
}) {
  if (!threadData) {
    return (
      <div className="flex items-center justify-center gap-2 py-8">
        <Clock className="text-muted-foreground size-4" />
        <span className="text-muted-foreground text-sm">No thread data</span>
      </div>
    );
  }

  return (
    <div className="space-y-4 p-4">
      <div>
        <h3 className="text-sm font-medium mb-2">Thread Information</h3>
        <div className="space-y-2 text-xs text-muted-foreground">
          <div>
            <span className="font-medium">ID:</span> {threadData.thread_id}
          </div>
          <div>
            <span className="font-medium">Title:</span> {displayThread?.title || '未命名'}
          </div>
          <div>
            <span className="font-medium">Status:</span> {displayThread?.status || threadData.status}
          </div>
          <div>
            <span className="font-medium">Last Activity:</span> {displayThread?.lastActivity || '未知'}
          </div>
          <div>
            <span className="font-medium">Created:</span>{" "}
            {new Date(threadData.created_at).toLocaleString()}
          </div>
          <div>
            <span className="font-medium">Updated:</span>{" "}
            {new Date(threadData.updated_at).toLocaleString()}
          </div>
          {(displayThread?.repository || threadData.metadata?.graph_id) && (
            <div>
              <span className="font-medium">Repository:</span>{" "}
              {displayThread?.repository || threadData.metadata.graph_id}
            </div>
          )}
          {displayThread?.branch && (
            <div>
              <span className="font-medium">Branch:</span> {displayThread.branch}
            </div>
          )}
          {displayThread?.taskCount !== undefined && (
            <div>
              <span className="font-medium">Tasks:</span> {displayThread.taskCount}
            </div>
          )}
        </div>
      </div>

      {threadData.values?.input && threadData.values.input.length > 0 && (
        <div>
          <h3 className="text-sm font-medium mb-2">Input Data</h3>
          <div className="bg-muted rounded p-2 text-xs">
            <pre className="whitespace-pre-wrap overflow-x-auto">
              {JSON.stringify(threadData.values.input, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}

// 调试面板
function DebugPanel({ threadData }: { threadData?: ThreadData | null }) {
  return (
    <div className="space-y-4 p-4">
      <div>
        <h3 className="text-sm font-medium mb-2">Debug Information</h3>
        {threadData ? (
          <div className="bg-muted rounded p-2 text-xs">
            <pre className="whitespace-pre-wrap overflow-x-auto">
              {JSON.stringify(threadData, null, 2)}
            </pre>
          </div>
        ) : (
          <div className="flex items-center justify-center gap-2 py-8">
            <Terminal className="text-muted-foreground size-4" />
            <span className="text-muted-foreground text-sm">No debug data</span>
          </div>
        )}
      </div>
    </div>
  );
}

export function ChatSidebarPanel({ threadData, displayThread, isLoading }: ChatSidebarPanelProps) {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col px-4 pt-4" style={{ height: "calc(100vh - 3rem)" }}>
      <div className="min-h-0 flex-1">
        <Tabs defaultValue="info" className="flex h-full w-full flex-col">
          <div className="flex flex-shrink-0 items-center gap-3">
            <TabsList className="bg-muted/70">
              <TabsTrigger value="info">Info</TabsTrigger>
              <TabsTrigger value="debug">Debug</TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="info" className="mb-2 flex-1">
            <Card className="border-border bg-card h-full p-0">
              <CardContent className="h-full p-0 overflow-y-auto">
                <ThreadInfoPanel threadData={threadData} displayThread={displayThread} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="debug" className="mb-2 flex-1">
            <Card className="border-border bg-card h-full p-0">
              <CardContent className="h-full p-0 overflow-y-auto">
                <DebugPanel threadData={threadData} />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
