"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, GitBranch } from "lucide-react";
import { cn } from "@/lib/utils";

interface ChatHeaderProps {
  title: string;
  threadId: string;
  status?: "running" | "completed" | "paused" | "error" | "idle";
  repository?: string;
  onBack: () => void;
}

// 获取状态点颜色
function getStatusDotColor(status: string) {
  switch (status) {
    case "running":
      return "bg-blue-500 dark:bg-blue-400";
    case "completed":
      return "bg-green-500 dark:bg-green-400";
    case "paused":
      return "bg-yellow-500 dark:bg-yellow-400";
    case "error":
      return "bg-red-500 dark:bg-red-400";
    default:
      return "bg-gray-500 dark:bg-gray-400";
  }
}

export function ChatHeader({
  title,
  threadId,
  status = "idle",
  repository,
  onBack,
}: ChatHeaderProps) {
  return (
    <div className="border-border bg-card absolute top-0 right-0 left-0 z-10 border-b px-4 py-2">
      <div className="flex items-center gap-3">
        <Button
          variant="ghost"
          size="sm"
          className="text-muted-foreground hover:bg-muted hover:text-foreground h-6 w-6 p-0"
          onClick={onBack}
        >
          <ArrowLeft className="h-3 w-3" />
        </Button>
        <div className="flex min-w-0 flex-1 items-center gap-2">
          <div
            className={cn(
              "size-2 flex-shrink-0 rounded-full",
              getStatusDotColor(status),
            )}
          ></div>
          <span className="text-muted-foreground max-w-[500px] truncate font-mono text-sm">
            {title}
          </span>
          <span className="text-muted-foreground text-xs">•</span>
          <span className="text-muted-foreground truncate text-xs">
            {threadId.slice(0, 8)}...
          </span>
          {repository && (
            <>
              <span className="text-muted-foreground text-xs">•</span>
              <GitBranch className="text-muted-foreground h-3 w-3" />
              <span className="text-muted-foreground truncate text-xs">
                {repository}
              </span>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
