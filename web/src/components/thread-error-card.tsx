"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, ArrowLeft } from "lucide-react";

interface ThreadStatusError {
  message: string;
  type: string;
}

interface ThreadErrorCardProps {
  error: ThreadStatusError;
  onGoBack: () => void;
}

export function ThreadErrorCard({ error, onGoBack }: ThreadErrorCardProps) {
  return (
    <div className="flex min-h-screen items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl">线程加载失败</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-muted p-3">
            <p className="text-sm text-muted-foreground">
              <span className="font-medium">错误类型:</span> {error.type}
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              <span className="font-medium">错误信息:</span> {error.message}
            </p>
          </div>
          <Button onClick={onGoBack} className="w-full" variant="outline">
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回首页
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
