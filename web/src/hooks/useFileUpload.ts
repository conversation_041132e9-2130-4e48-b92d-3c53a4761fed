"use client";

import React, { useState, useRef, useCallback } from "react";
import { Base64ContentBlock } from "@/types/content-block";
import { v4 as uuidv4 } from "uuid";

export interface FileUploadState {
  contentBlocks: Base64ContentBlock[];
  setContentBlocks: React.Dispatch<React.SetStateAction<Base64ContentBlock[]>>;
  dropRef: React.RefObject<HTMLDivElement | null>;
  removeBlock: (id: string) => void;
  dragOver: boolean;
  handlePaste: (e: React.ClipboardEvent<HTMLTextAreaElement>) => void;
}

export function useFileUpload(): FileUploadState {
  const [contentBlocks, setContentBlocks] = useState<Base64ContentBlock[]>([]);
  const [dragOver, setDragOver] = useState(false);
  const dropRef = useRef<HTMLDivElement>(null);

  const removeBlock = useCallback((id: string) => {
    setContentBlocks(prev => prev.filter(block => block.id !== id));
  }, []);

  const addContentBlock = useCallback((file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result;
      if (typeof result === "string") {
        const base64Data = result.split(",")[1]; // Remove data:image/...;base64, prefix
        const newBlock: Base64ContentBlock = {
          id: uuidv4(),
          type: "image_url",
          image_url: {
            url: `data:${file.type};base64,${base64Data}`,
          },
        };
        setContentBlocks(prev => [...prev, newBlock]);
      }
    };
    reader.readAsDataURL(file);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    files.forEach(file => {
      if (file.type.startsWith("image/")) {
        addContentBlock(file);
      }
    });
  }, [addContentBlock]);

  const handlePaste = useCallback((e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const items = Array.from(e.clipboardData.items);
    items.forEach(item => {
      if (item.type.startsWith("image/")) {
        const file = item.getAsFile();
        if (file) {
          addContentBlock(file);
        }
      }
    });
  }, [addContentBlock]);

  // Set up drag and drop event listeners
  React.useEffect(() => {
    const element = dropRef.current;
    if (!element) return;

    element.addEventListener("dragover", handleDragOver as any);
    element.addEventListener("dragleave", handleDragLeave as any);
    element.addEventListener("drop", handleDrop as any);

    return () => {
      element.removeEventListener("dragover", handleDragOver as any);
      element.removeEventListener("dragleave", handleDragLeave as any);
      element.removeEventListener("drop", handleDrop as any);
    };
  }, [handleDragOver, handleDragLeave, handleDrop]);

  return {
    contentBlocks,
    setContentBlocks,
    dropRef,
    removeBlock,
    dragOver,
    handlePaste,
  };
}
