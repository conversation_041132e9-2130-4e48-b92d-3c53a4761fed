"use client";

import { useState, useEffect, useCallback } from "react";

const DRAFT_STORAGE_KEY = "langgraph-mvp-draft";
const DRAFT_SAVE_DELAY = 1000; // 1秒延迟保存

export interface DraftStorageState {
  message: string;
  setMessage: (message: string) => void;
  clearCurrentDraft: () => void;
  hasDraft: boolean;
}

export function useDraftStorage(): DraftStorageState {
  const [message, setMessageState] = useState("");
  const [hasDraft, setHasDraft] = useState(false);

  // 从localStorage加载草稿
  useEffect(() => {
    try {
      const savedDraft = localStorage.getItem(DRAFT_STORAGE_KEY);
      if (savedDraft) {
        setMessageState(savedDraft);
        setHasDraft(true);
      }
    } catch (error) {
      console.error("Failed to load draft from localStorage:", error);
    }
  }, []);

  // 保存草稿到localStorage（带延迟）
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      try {
        if (message.trim()) {
          localStorage.setItem(DRAFT_STORAGE_KEY, message);
          setHasDraft(true);
        } else {
          localStorage.removeItem(DRAFT_STORAGE_KEY);
          setHasDraft(false);
        }
      } catch (error) {
        console.error("Failed to save draft to localStorage:", error);
      }
    }, DRAFT_SAVE_DELAY);

    return () => clearTimeout(timeoutId);
  }, [message]);

  const setMessage = useCallback((newMessage: string) => {
    setMessageState(newMessage);
  }, []);

  const clearCurrentDraft = useCallback(() => {
    try {
      localStorage.removeItem(DRAFT_STORAGE_KEY);
      setHasDraft(false);
    } catch (error) {
      console.error("Failed to clear draft from localStorage:", error);
    }
  }, []);

  return {
    message,
    setMessage,
    clearCurrentDraft,
    hasDraft,
  };
}
