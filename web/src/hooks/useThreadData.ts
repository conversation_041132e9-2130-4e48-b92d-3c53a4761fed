"use client";

import { useState } from "react";

// 历史消息接口
interface HistoryMessage {
  content: string;
  tool_calls: any[];
  invalid_tool_calls: any[];
  additional_kwargs: any;
  response_metadata: any;
  id: string;
  type: "ai" | "human";
}

// 从 LangGraph 消息中提取内容的函数
function getMessageContentString(content: any): string {
  if (typeof content === 'string') {
    return content;
  }
  if (Array.isArray(content)) {
    return content.map(item => {
      if (typeof item === 'string') return item;
      if (item.text) return item.text;
      return JSON.stringify(item);
    }).join('');
  }
  if (content && typeof content === 'object' && content.text) {
    return content.text;
  }
  return JSON.stringify(content);
}

// 转换历史消息为本地消息格式
function convertHistoryToMessages(historyMessages: HistoryMessage[]) {
  return historyMessages.map((msg) => ({
    id: msg.id,
    role: msg.type === 'human' ? 'user' : 'assistant' as 'user' | 'assistant',
    content: getMessageContentString(msg.content),
    timestamp: new Date(),
  }));
}

// 简化的 hook，不进行任何 API 调用，直接返回 dummy 数据
export function useThreadData(threadId: string, selectedAgent: string) {
  const [isLoading, setIsLoading] = useState(false); // 直接设为 false，不加载
  const [error] = useState<string | null>(null);

  // 直接创建 dummy thread 对象，不进行任何 API 调用
  const dummyThread = {
    thread_id: threadId,
    values: {},
    status: "idle" as const,
    updated_at: new Date().toISOString(),
    created_at: new Date().toISOString(),
    metadata: {
      graph_id: selectedAgent,
      assistant_id: selectedAgent,
    },
    config: {},
  };

  // 空的历史消息
  const historyMessages: any[] = [];

  return {
    threadData: dummyThread,
    historyMessages,
    isLoading,
    error,
    refetch: () => {
      // 空的 refetch 函数
    },
  };
}
