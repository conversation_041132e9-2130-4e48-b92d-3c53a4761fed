import { initApiPassthrough } from "langgraph-nextjs-api-passthrough";

// API代理，将请求转发到langgraph服务器
export const { GET, POST, PUT, PATCH, DELETE, OPTIONS, runtime } =
  initApiPassthrough({
    apiUrl: process.env.LANGGRAPH_API_URL ?? "http://localhost:2024",
    runtime: "edge",
    disableWarningLog: true,
    headers: async (req) => {
      return {
        "Content-Type": "application/json",
      };
    },
  });
