"use client";

import React, { useState } from "react";
import { ThreadList } from "@/components/thread-list";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";

/**
 * 线程管理页面
 */
export default function ThreadsPage() { 
  const router = useRouter();
  const [selectedAssistant, setSelectedAssistant] = useState<string>("all");
  const [currentUserId, setCurrentUserId] = useState<string>("");
  const [pageSize, setPageSize] = useState<number>(10);

  const handleThreadClick = (threadId: string) => {
    router.push(`/chat/${threadId}`);
  };

  const assistantOptions = [
    { value: "all", label: "所有助手" },
    { value: "designToCode", label: "设计转代码" },
    { value: "simpleChat", label: "简单聊天" },
  ];

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">线程管理</h1>
        <p className="text-gray-600">
          threads历史
        </p>
      </div>

      {/* 过滤器 */}
      <Card className="mb-5">
        <CardHeader>
          <CardTitle>过滤选项</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">助手类型</label>
              <Select value={selectedAssistant} onValueChange={setSelectedAssistant}>
                <SelectTrigger>
                  <SelectValue placeholder="选择助手" />
                </SelectTrigger>
                <SelectContent>
                  {assistantOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">用户ID</label>
              <Input
                placeholder="输入用户ID"
                value={currentUserId}
                onChange={(e) => setCurrentUserId(e.target.value)}
              />
            </div>

            <div>
              <label className="text-sm font-medium mb-2 block">每页大小</label>
              <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5</SelectItem>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>基础线程列表</CardTitle>
        </CardHeader>
        <CardContent>
          <ThreadList
            assistantId={selectedAssistant === "all" ? undefined : selectedAssistant}
            currentUserId={currentUserId || undefined}
            limit={pageSize}
            onThreadClick={handleThreadClick}
            showUserInfo={true}
          />
        </CardContent>
      </Card>
    </div>
  );
}
