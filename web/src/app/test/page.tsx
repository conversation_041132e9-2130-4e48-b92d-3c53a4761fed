"use client";

import { useState } from "react";

export default function TestPage() {
  const [result, setResult] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const testApiConnection = async () => {
    setLoading(true);
    setResult("测试中...");
    
    try {
      // 测试直接连接到LangGraph API
      const response = await fetch("/api/threads", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          assistant_id: "designToCode",
          metadata: { test: true }
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        setResult(`✅ API连接成功！响应: ${JSON.stringify(data, null, 2)}`);
      } else {
        setResult(`❌ API响应错误: ${response.status} ${response.statusText}`);
      }
    } catch (error: any) {
      setResult(`❌ 连接失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">LangGraph API 测试</h1>
        
        <div className="space-y-4">
          <button
            onClick={testApiConnection}
            disabled={loading}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? "测试中..." : "测试API连接"}
          </button>
          
          <div className="p-4 border rounded-lg bg-gray-50">
            <h3 className="font-semibold mb-2">测试结果:</h3>
            <pre className="whitespace-pre-wrap text-sm">{result || "点击按钮开始测试"}</pre>
          </div>
        </div>
        
        <div className="mt-8 space-y-2 text-sm text-gray-600">
          <p>• Web服务: http://localhost:3000</p>
          <p>• LangGraph API: http://localhost:2024</p>
          <p>• API代理: /api/*</p>
        </div>
      </div>
    </div>
  );
}
