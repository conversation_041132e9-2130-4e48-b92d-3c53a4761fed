"use client";

import { use<PERSON><PERSON><PERSON>, useSearch<PERSON>ara<PERSON> } from "next/navigation";
import { ChatInterface } from "@/components/chat-interface";
import { useState, useMemo, useEffect, useRef, use } from "react";
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { DEFAULT_AGENT } from "@/common/constants";
import { useStream } from "@langchain/langgraph-sdk/react";
import { Client, Thread } from "@langchain/langgraph-sdk";
import { ThreadErrorCard } from "@/components/thread-error-card";
import { ThreadViewLoading } from "@/components/thread-view-loading";
import { useThreadsSWR } from "@/hooks/useThreadsSWR";
import { useThreadMetadata } from "@/hooks/useThreadMetadata";
import { DEFAULT_API_URL } from "@/providers/client";

// 获取初始线程数据的函数
async function fetchInitialThread(
  client: Client,
  threadId: string,
  reqCount = 0,
): Promise<{ thread: Thread | null; notFound: boolean }> {
  try {
    const thread = await client.threads.get(threadId);
    return { thread, notFound: false };
  } catch (e: any) {
    console.error("Failed to fetch thread", {
      requestCount: reqCount,
      error: e,
    });

    // 如果是404错误，表示线程不存在
    if (e?.status === 404) {
      return { thread: null, notFound: true };
    }

    // 重试最多 5 次（仅对非404错误）
    if (reqCount < 5) {
      return fetchInitialThread(client, threadId, reqCount + 1);
    }
    return { thread: null, notFound: false };
  }
}

interface ChatPageProps {
  thread_id: string;
}

export default function ChatPage({
  params,
}: {
  params: Promise<ChatPageProps>;
}) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { thread_id } = use(params);
  const threadId = thread_id;
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [initialFetchedThread, setInitialFetchedThread] = useState<Thread | null>(null);
  const [threadNotFound, setThreadNotFound] = useState(false);

  // 动态计算 selectedAgent
  const selectedAgent = searchParams.get('agent') || DEFAULT_AGENT;

  // 使用 useMemo 稳定 stream 配置，避免重复创建
  const streamConfig = useMemo(() => ({
    apiUrl: DEFAULT_API_URL,
    assistantId: selectedAgent,
    threadId: threadId,
    reconnectOnMount: true,
    fetchStateHistory: false,
  }), [selectedAgent, threadId]);

  // 创建 stream
  const stream = useStream(streamConfig);

  // 使用 useThreadsSWR 获取线程列表
  const { threads, isLoading: threadsLoading } = useThreadsSWR({
    assistantId: selectedAgent,
    disableUserFiltering: true,
  });

  // 从线程列表中查找当前线程
  const thread = threads.find((t) => t.thread_id === threadId);

  // 创建 dummy thread 对象，如果没有找到线程
  const dummyThread = thread ||
    initialFetchedThread || {
      thread_id: threadId,
      values: {},
      status: "idle" as const,
      updated_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      metadata: {
        graph_id: selectedAgent,
        assistant_id: selectedAgent,
      },
      config: {},
    };

  // 使用 useThreadMetadata hook 处理线程元数据
  const { metadata: currentDisplayThread, statusError } = useThreadMetadata(
    dummyThread as any,
  );

  const handleBackToHome = () => {
    router.push('/');
  };

  // 初始线程获取逻辑
  const initialThreadFetched = useRef(false);
  useEffect(() => {
    if (!thread && !initialFetchedThread && !initialThreadFetched.current) {
      fetchInitialThread(stream.client as Client, threadId)
        .then(({ thread: fetchedThread, notFound }) => {
          if (notFound) {
            setThreadNotFound(true);
          } else {
            setInitialFetchedThread(fetchedThread);
          }
        })
        .finally(() => (initialThreadFetched.current = true));
    }

    if (initialThreadFetched.current && initialFetchedThread && thread) {
      setInitialFetchedThread(null);
    }
  }, [threadId, thread]);

  // 如果没有 threadId，返回 null
  if (!threadId) {
    return null;
  }

  // 错误处理
  if (statusError && "message" in statusError && "type" in statusError) {
    return (
      <ThreadErrorCard
        error={statusError}
        onGoBack={handleBackToHome}
      />
    );
  }

  // 线程不存在错误处理
  if (threadNotFound || (initialThreadFetched.current && !thread && !initialFetchedThread)) {
    return (
      <ThreadErrorCard
        error={{
          message: "线程不存在或已被删除",
          type: "not_found"
        }}
        onGoBack={handleBackToHome}
      />
    );
  }

  // 加载状态处理
  if (
    (!thread || threadsLoading) &&
    (!initialFetchedThread || !initialThreadFetched.current)
  ) {
    return <ThreadViewLoading onBackToHome={handleBackToHome} />;
  }

  return (
    <div className="bg-background fixed inset-0">
      <div className="h-screen flex bg-background">
        {/* 移动端侧边栏遮罩 */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* 主聊天区域 */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* 移动端顶部栏 */}
          <div className="lg:hidden flex items-center gap-2 p-4 border-b bg-background">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(true)}
            >
              <Menu className="h-4 w-4" />
            </Button>
            <div className="flex-1">
              <h1 className="font-semibold truncate">
                {currentDisplayThread.title}
              </h1>
            </div>
          </div>

          {/* 聊天界面 */}
          <div className="flex-1">
            <ChatInterface
              onBack={handleBackToHome}
              threadId={threadId}
              selectedAgent={selectedAgent}
              stream={stream}
              initialThread={dummyThread}
              displayThread={currentDisplayThread}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
