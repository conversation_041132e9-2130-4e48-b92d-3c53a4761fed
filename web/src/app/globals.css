@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.87 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.5rem;
}

.dark {
  --background: oklch(0.29 0.005 280);
  --foreground: oklch(0.92 0.01 280);
  --card: oklch(0.32 0.005 280);
  --card-foreground: oklch(0.92 0.01 280);
  --popover: oklch(0.32 0.005 280);
  --popover-foreground: oklch(0.92 0.01 280);
  --primary: oklch(0.92 0.01 280);
  --primary-foreground: oklch(0.24 0.005 280);
  --secondary: oklch(0.38 0.005 280);
  --secondary-foreground: oklch(0.92 0.01 280);
  --muted: oklch(0.35 0.005 280);
  --muted-foreground: oklch(0.65 0.01 280);
  --accent: oklch(0.38 0.005 280);
  --accent-foreground: oklch(0.92 0.01 280);
  --destructive: oklch(0.55 0.15 20);
  --destructive-foreground: oklch(0.95 0.01 280);
  --border: oklch(0.42 0.005 280);
  --input: oklch(0.42 0.005 280);
  --ring: oklch(0.5 0.01 280);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .shadow-inner-right {
    box-shadow: inset -9px 0 6px -1px rgb(0 0 0 / 0.02);
  }

  .shadow-inner-left {
    box-shadow: inset 9px 0 6px -1px rgb(0 0 0 / 0.02);
  }

  .scrollbar-pretty-auto {
    overflow-y: auto;
  }

  .scrollbar-pretty-auto::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar-pretty-auto::-webkit-scrollbar-thumb {
    border-radius: 9999px;
    background-color: rgb(209 213 219);
  }

  .scrollbar-pretty-auto::-webkit-scrollbar-track {
    background-color: transparent;
  }

  .dark .scrollbar-pretty-auto::-webkit-scrollbar-thumb {
    background-color: rgb(95 105 119);
  }
}
