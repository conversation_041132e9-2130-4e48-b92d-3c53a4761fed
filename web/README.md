# LangGraph MVP Demo Web 界面

这是一个基于 Next.js 的 Web 界面，用于与 LangGraph Agent 服务进行交互。

## 功能特性

- 🤖 **智能对话**: 与 designToCode Agent 进行实时对话
- ⚡ **流式处理**: 支持流式响应，实时显示 Agent 处理结果
- 🎨 **现代UI**: 基于 Tailwind CSS 和 shadcn/ui 的现代化界面
- 🌙 **主题切换**: 支持明暗主题切换
- 📱 **响应式设计**: 适配不同设备尺寸

## 技术栈

- **框架**: Next.js 15 (App Router)
- **UI库**: shadcn/ui + Radix UI
- **样式**: Tailwind CSS
- **状态管理**: React hooks
- **API客户端**: @langchain/langgraph-sdk
- **图标**: Lucide React
- **通知**: Sonner

## 前置要求

1. Node.js >= 18.0.0
2. pnpm
3. LangGraph API 服务运行在 `http://localhost:2024`

## 安装和运行

### 1. 安装依赖

```bash
cd web
pnpm install
```

### 2. 环境配置

创建 `.env.local` 文件（可选，使用默认配置）:

```bash
# LangGraph API URL
LANGGRAPH_API_URL=http://localhost:2024

# Next.js 配置
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 3. 启动 LangGraph 服务

确保在项目根目录下的 LangGraph 服务已启动：

```bash
# 在项目根目录
npm run build
npm start
# 或者使用开发模式
npm run dev
```

LangGraph 服务应该运行在 `http://localhost:2024`

### 4. 启动 Web 应用

```bash
# 在 web 目录
pnpm dev
```

应用将运行在 `http://localhost:3000`

## 项目结构

```
web/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API 代理路由
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── page.tsx           # 首页
│   ├── components/            # React 组件
│   │   ├── ui/               # shadcn/ui 组件
│   │   ├── chat-interface.tsx # 聊天界面
│   │   └── theme-*.tsx       # 主题相关组件
│   ├── lib/                  # 工具函数
│   └── hooks/                # 自定义 hooks
├── package.json
├── next.config.mjs
├── tailwind.config.js
└── tsconfig.json
```

## 使用方法

1. **访问首页**: 打开 `http://localhost:3000`
2. **开始对话**: 点击"开始对话"按钮
3. **发送消息**: 在输入框中输入消息，按 Enter 发送
4. **查看响应**: Agent 会流式返回处理结果
5. **停止处理**: 在 Agent 处理过程中可以点击停止按钮取消

## API 代理

Web 应用通过 `/api/[..._path]` 路由代理所有对 LangGraph 服务的请求，避免跨域问题。

## 开发说明

- 使用 TypeScript 进行类型安全开发
- 基于 shadcn/ui 组件库构建 UI
- 支持深色模式
- 响应式设计适配移动端

## 故障排除

### 常见问题

1. **连接失败**: 确保 LangGraph 服务在 `localhost:2024` 运行
2. **样式问题**: 确保已正确安装 Tailwind CSS 依赖
3. **构建错误**: 检查 TypeScript 类型错误

### 调试

1. 检查浏览器控制台错误信息
2. 检查 Network 标签页查看 API 请求状态
3. 确认 LangGraph 服务日志

## 部署

### 构建生产版本

```bash
pnpm build
pnpm start
```

### Docker 部署（可选）

可以创建 Dockerfile 进行容器化部署：

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install
COPY . .
RUN pnpm build
EXPOSE 3000
CMD ["pnpm", "start"]
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

MIT License
