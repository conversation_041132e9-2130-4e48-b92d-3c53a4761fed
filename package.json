{"name": "ht-langchain-mvp", "version": "1.0.0", "description": "基于LangChain.js和LangGraph的多智能体系统MVP", "type": "module", "main": "dist/index.js", "scripts": {"build": "tsc && npm run copy-assets", "copy-assets": "node scripts/copy-assets.js", "start": "node dist/server.js", "dev": "langgraphjs dev --no-browser --config langgraph.json", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts", "web:install": "cd web && pnpm install", "web:dev": "cd web && pnpm dev", "web:build": "cd web && pnpm build", "web:start": "cd web && pnpm start", "dev:all": "concurrently \"npm run dev\" \"npm run web:dev\"", "script:test-api-run": "node --loader ts-node/esm scripts/test-api-run.ts", "script:test-graph": "node scripts/test-graph.js", "script:test-workflow": "node --loader ts-node/esm scripts/test-workflow.ts", "script:test-api-assistant": "node --loader ts-node/esm scripts/test-api-assistant.ts", "script:test-api-thread": "node --loader ts-node/esm scripts/test-api-thread.ts"}, "keywords": ["langchain", "langgraph", "multi-agent", "mongodb", "typescript"], "author": "HT Team", "license": "MIT", "dependencies": {"@langchain/community": "^0.3.53", "@langchain/core": "^0.3.72", "@langchain/google-genai": "^0.2.16", "@langchain/langgraph": "^0.4.6", "@langchain/langgraph-api": "^0.0.60", "@langchain/langgraph-sdk": "^0.0.95", "@langchain/openai": "^0.6.9", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "hono": "^4.9.4", "joi": "^17.11.0", "langchain": "^0.3.31", "mongodb": "^6.3.0", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"@langchain/langgraph-cli": "^0.0.47", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}