version: "3.8"

services:
  mongodb:
    image: mongo:7.0
    container_name: mongodb-haicode
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: gemini
      MONGO_INITDB_ROOT_PASSWORD: 1234568
      MONGO_INITDB_DATABASE: haicode_cli
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init:/docker-entrypoint-initdb.d
    networks:
      - mongodb-network

volumes:
  mongodb_data:
    driver: local

networks:
  mongodb-network:
    driver: bridge
