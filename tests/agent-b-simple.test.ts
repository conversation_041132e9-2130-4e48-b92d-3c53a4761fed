import { AgentB } from '../src/agents/agent-b';

describe('Agent B StateGraph Simple Test', () => {
  const TEST_TIMEOUT = 30000; // 30秒超时

  test('should initialize with StateGraph successfully', () => {
    const agentB = new AgentB();
    
    // 验证Agent B实例化成功
    expect(agentB).toBeDefined();
    expect(agentB.getConfig()).toBeDefined();
    expect(agentB.getConfig().name).toBe('Agent B');
    expect(agentB.getConfig().implementation).toBe('StateGraph');
  });

  test('should execute simple iterative analysis using StateGraph', async () => {
    const agentB = new AgentB();
    const testInput = '请分析人工智能的发展趋势';
    
    const result = await agentB.execute(testInput, 'test-session-simple');
    
    // 验证基本结果结构
    expect(result.success).toBe(true);
    expect(result.result).toBeDefined();
    expect(typeof result.result).toBe('string');
    expect(result.result.length).toBeGreaterThan(0);
    expect(result.execution_time).toBeGreaterThan(0);
    
    // 验证结果内容质量
    expect(result.result).toContain('人工智能');
    expect(result.result.length).toBeGreaterThan(50); // 确保有足够的分析内容
    
    console.log('✅ StateGraph execution successful!');
    console.log('Result length:', result.result.length);
    console.log('Execution time:', result.execution_time, 'ms');
  }, TEST_TIMEOUT);

  test('should handle error cases correctly', async () => {
    const agentB = new AgentB();
    
    // 测试空输入
    await expect(agentB.execute('', 'test-session-error'))
      .rejects.toThrow('Input cannot be empty');
    
    console.log('✅ Error handling working correctly!');
  });

  test('should pass health check', async () => {
    const agentB = new AgentB();
    
    const isHealthy = await agentB.healthCheck();
    expect(isHealthy).toBe(true);
    
    console.log('✅ Health check passed!');
  }, TEST_TIMEOUT);
});
