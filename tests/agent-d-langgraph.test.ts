import { AgentD } from '../src/agents/agent-d';
import { AgentError } from '../src/types';

describe('Agent D LangGraph Implementation', () => {
  const TEST_TIMEOUT = 60000; // 60秒超时

  beforeAll(async () => {
    // 测试环境初始化
    console.log('Starting Agent D LangGraph tests...');
  });

  afterAll(async () => {
    // 清理测试环境
    console.log('Agent D LangGraph tests completed.');
  });

  describe('LangGraph StateGraph Tests', () => {
    test('should initialize StateGraph workflow correctly', () => {
      const agentD = new AgentD();
      
      // 验证Agent D实例化成功
      expect(agentD).toBeDefined();
      expect(agentD.getConfig()).toBeDefined();
      expect(agentD.getConfig().name).toBe('Agent D');
      expect(agentD.getConfig().implementation).toBe('StateGraph');
      expect(agentD.getConfig().role).toBe('aggregation');
    });

    test('should execute multi-agent aggregation using StateGraph', async () => {
      const agentD = new AgentD();
      const testInput = {
        original_input: '请分析人工智能的发展趋势',
        agent_a_result: 'Agent A 深度分析：人工智能正在快速发展，涉及机器学习、深度学习等多个领域。',
        agent_b_result: 'Agent B 迭代分析：经过多轮分析，人工智能的发展呈现出技术突破、应用扩展、伦理挑战等特点。',
        agent_c_result: 'Agent C 函数处理：通过函数调用分析，人工智能在自动化、决策支持、数据处理等方面具有巨大潜力。'
      };
      
      const result = await agentD.execute(testInput, 'test-session-langgraph-1');
      
      // 验证基本结果结构
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      expect(typeof result.result).toBe('string');
      expect(result.result.length).toBeGreaterThan(0);
      expect(result.execution_time).toBeGreaterThan(0);
      expect(result.tokens_used).toBeGreaterThan(0);
      
      // 验证聚合结果质量
      expect(result.result).toContain('人工智能');
      expect(result.result.length).toBeGreaterThan(100); // 确保有足够的聚合内容
      
      // 验证聚合分析的特征
      expect(result.result).toContain('Agent D');
      expect(result.result).toContain('整合');
    }, TEST_TIMEOUT);

    test('should handle complex multi-agent aggregation task', async () => {
      const agentD = new AgentD();
      const testInput = {
        original_input: '分析云计算技术在企业数字化转型中的作用',
        agent_a_result: 'Agent A 分析：云计算提供了弹性计算资源，支持企业快速扩展和成本优化。包括IaaS、PaaS、SaaS等服务模式。',
        agent_b_result: 'Agent B 迭代分析：经过深入思考，云计算在数字化转型中的核心价值体现在：1）基础设施现代化 2）业务敏捷性提升 3）数据驱动决策 4）创新能力增强。',
        agent_c_result: 'Agent C 函数处理：通过技术分析函数，云计算技术栈包括虚拟化、容器化、微服务架构、DevOps等关键技术。'
      };
      
      const result = await agentD.execute(testInput, 'test-session-langgraph-2');
      
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      expect(result.result.length).toBeGreaterThan(200);
      expect(result.execution_time).toBeGreaterThan(0);
      
      // 验证聚合的深度和结构
      const content = result.result.toLowerCase();
      expect(content).toContain('云计算');
      expect(content).toContain('数字化');
      expect(content).toContain('企业');
    }, TEST_TIMEOUT);

    test('should validate inputs correctly', async () => {
      const agentD = new AgentD();
      
      // 测试缺少必需输入
      const incompleteInput = {
        original_input: '测试输入',
        agent_a_result: 'Agent A 结果',
        // 缺少 agent_b_result 和 agent_c_result
      };
      
      await expect(agentD.execute(incompleteInput, 'test-session-validation'))
        .rejects.toThrow();
    });

    test('should handle empty inputs correctly', async () => {
      const agentD = new AgentD();
      
      // 测试空输入
      const emptyInput = {
        original_input: '',
        agent_a_result: '',
        agent_b_result: '',
        agent_c_result: ''
      };
      
      await expect(agentD.execute(emptyInput, 'test-session-empty'))
        .rejects.toThrow();
    });
  });

  describe('Aggregation Quality Tests', () => {
    test('should produce enhanced response with execution summary', async () => {
      const agentD = new AgentD();
      const testInput = {
        original_input: '分析区块链技术的应用前景',
        agent_a_result: 'Agent A：区块链技术具有去中心化、不可篡改、透明等特性。',
        agent_b_result: 'Agent B：区块链在金融、供应链、数字身份等领域有广泛应用。',
        agent_c_result: 'Agent C：技术实现包括共识机制、智能合约、分布式账本等。'
      };
      
      const result = await agentD.execute(testInput, 'test-session-quality');
      
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      
      // 验证包含执行摘要
      expect(result.result).toContain('执行摘要');
      expect(result.result).toContain('协作智能体');
      
      // 验证包含质量指标
      expect(result.result).toContain('质量指标');
      expect(result.result).toContain('回答长度');
    }, TEST_TIMEOUT);

    test('should maintain consistent aggregation format', async () => {
      const agentD = new AgentD();
      const testInputs = [
        {
          original_input: '分析人工智能的发展',
          agent_a_result: 'AI技术快速发展',
          agent_b_result: 'AI应用广泛扩展',
          agent_c_result: 'AI技术栈完善'
        },
        {
          original_input: '评估云计算的优势',
          agent_a_result: '云计算提供弹性资源',
          agent_b_result: '云计算降低成本',
          agent_c_result: '云计算支持创新'
        }
      ];
      
      for (let i = 0; i < testInputs.length; i++) {
        const result = await agentD.execute(testInputs[i], `test-session-format-${i}`);
        
        expect(result.success).toBe(true);
        expect(result.result).toBeDefined();
        expect(typeof result.result).toBe('string');
        expect(result.execution_time).toBeGreaterThan(0);
        expect(result.tokens_used).toBeGreaterThan(0);
      }
    }, TEST_TIMEOUT * 2);
  });

  describe('Performance and Reliability Tests', () => {
    test('should handle concurrent aggregation requests', async () => {
      const agentD = new AgentD();
      const testInputs = [
        {
          original_input: '分析AI发展',
          agent_a_result: 'AI分析结果A',
          agent_b_result: 'AI分析结果B',
          agent_c_result: 'AI分析结果C'
        },
        {
          original_input: '评估云计算',
          agent_a_result: '云计算分析A',
          agent_b_result: '云计算分析B',
          agent_c_result: '云计算分析C'
        }
      ];
      
      const promises = testInputs.map((input, index) => 
        agentD.execute(input, `concurrent-session-${index}`)
      );
      
      const results = await Promise.all(promises);
      
      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.result).toBeDefined();
        expect(result.result.length).toBeGreaterThan(0);
      });
    }, TEST_TIMEOUT);

    test('should pass health check', async () => {
      const agentD = new AgentD();
      
      const isHealthy = await agentD.healthCheck();
      expect(isHealthy).toBe(true);
    }, TEST_TIMEOUT);

    test('should return correct configuration', () => {
      const agentD = new AgentD();
      const config = agentD.getConfig();
      
      expect(config).toBeDefined();
      expect(config.name).toBe('Agent D');
      expect(config.description).toContain('最终智能体');
      expect(config.model).toBeDefined();
      expect(config.temperature).toBe(0.5);
      expect(config.maxTokens).toBe(3000);
      expect(config.role).toBe('aggregation');
      expect(config.input_sources).toEqual(['agent_a', 'agent_b', 'agent_c']);
      expect(config.output_type).toBe('final_response');
      expect(config.implementation).toBe('StateGraph');
      expect(config.workflow_nodes).toEqual(['validate_inputs', 'preprocess_inputs', 'aggregate_results', 'enhance_response', 'finalize_result']);
    });
  });

  describe('Backward Compatibility Tests', () => {
    test('should maintain same interface as original Agent D', async () => {
      const agentD = new AgentD();
      const testInput = {
        original_input: '测试向后兼容性',
        agent_a_result: 'Agent A 测试结果',
        agent_b_result: 'Agent B 测试结果',
        agent_c_result: 'Agent C 测试结果'
      };
      
      const result = await agentD.execute(testInput, 'test-session-compatibility');
      
      // 验证返回结果结构与原版本一致
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('result');
      expect(result).toHaveProperty('execution_time');
      expect(result).toHaveProperty('tokens_used');
      
      expect(typeof result.success).toBe('boolean');
      expect(typeof result.result).toBe('string');
      expect(typeof result.execution_time).toBe('number');
      expect(typeof result.tokens_used).toBe('number');
    }, TEST_TIMEOUT);
  });

  describe('StateGraph Specific Tests', () => {
    test('should demonstrate multi-agent aggregation capability', async () => {
      const agentD = new AgentD();
      const testInput = {
        original_input: '这是一个需要多智能体协作分析的复杂问题',
        agent_a_result: 'Agent A 提供了深度的理论分析和背景知识',
        agent_b_result: 'Agent B 通过迭代优化提供了多角度的见解和改进建议',
        agent_c_result: 'Agent C 通过函数调用提供了技术实现和数据处理的具体方案'
      };
      
      const result = await agentD.execute(testInput, 'test-session-aggregation');
      
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      
      // 验证结果包含多智能体聚合的特征
      const content = result.result;
      expect(content).toContain('整合');
      expect(content.length).toBeGreaterThan(200); // 确保有足够的聚合内容
      
      // 验证包含所有智能体的信息
      expect(content.toLowerCase()).toContain('agent');
    }, TEST_TIMEOUT);
  });
});
