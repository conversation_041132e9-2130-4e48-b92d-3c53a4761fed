import dotenv from 'dotenv';

// 加载测试环境变量
dotenv.config({ path: '.env.test' });

// 设置测试环境
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // 减少测试时的日志输出

// 设置测试超时
jest.setTimeout(60000);

// 全局测试设置
beforeAll(() => {
  console.log('🧪 Starting Multi-Agent System Tests');
  console.log('Environment:', process.env.NODE_ENV);
  console.log('MongoDB URI:', process.env.MONGODB_URI?.replace(/\/\/.*@/, '//***@'));
});

afterAll(() => {
  console.log('✅ All tests completed');
});
