import { AgentB } from '../src/agents/agent-b';
import { AgentError } from '../src/types';

describe('Agent B LangGraph Implementation', () => {
  const TEST_TIMEOUT = 60000; // 60秒超时

  beforeAll(async () => {
    // 测试环境初始化
    console.log('Starting Agent B LangGraph tests...');
  });

  afterAll(async () => {
    // 清理测试环境
    console.log('Agent B LangGraph tests completed.');
  });

  describe('LangGraph StateGraph Tests', () => {
    test('should initialize StateGraph workflow correctly', () => {
      const agentB = new AgentB();
      
      // 验证Agent B实例化成功
      expect(agentB).toBeDefined();
      expect(agentB.getConfig()).toBeDefined();
      expect(agentB.getConfig().name).toBe('Agent B');
      expect(agentB.getConfig().implementation).toBe('StateGraph');
      expect(agentB.getConfig().supports_iteration).toBe(true);
    });

    test('should execute simple iterative analysis using StateGraph', async () => {
      const agentB = new AgentB();
      const testInput = '请分析人工智能的发展趋势';
      
      const result = await agentB.execute(testInput, 'test-session-langgraph-1');
      
      // 验证基本结果结构
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      expect(typeof result.result).toBe('string');
      expect(result.result.length).toBeGreaterThan(0);
      expect(result.execution_time).toBeGreaterThan(0);
      expect(result.tokens_used).toBeGreaterThan(0);
      
      // 验证结果内容质量
      expect(result.result).toContain('人工智能');
      expect(result.result.length).toBeGreaterThan(50); // 确保有足够的分析内容
      
      // 验证迭代分析的特征
      expect(result.result).toContain('Agent B');
      expect(result.result).toContain('分析');
    }, TEST_TIMEOUT);

    test('should handle complex iterative analysis task', async () => {
      const agentB = new AgentB();
      const testInput = '分析云计算技术在企业数字化转型中的作用，包括技术优势、实施挑战和未来发展方向';
      
      const result = await agentB.execute(testInput, 'test-session-langgraph-2');
      
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      expect(result.result.length).toBeGreaterThan(100);
      expect(result.execution_time).toBeGreaterThan(0);
      
      // 验证分析的深度和结构
      const content = result.result.toLowerCase();
      expect(content).toContain('云计算');
      expect(content).toContain('数字化');
    }, TEST_TIMEOUT);

    test('should support iteration with currentIteration parameter', async () => {
      const agentB = new AgentB();
      const testInput = '分析区块链技术的应用前景';
      
      // 测试从第0次迭代开始
      const result1 = await agentB.execute(testInput, 'test-session-iteration-1', 0);
      expect(result1.success).toBe(true);
      
      // 测试从第1次迭代开始
      const result2 = await agentB.execute(testInput, 'test-session-iteration-2', 1);
      expect(result2.success).toBe(true);
      
      // 两次结果应该都有效
      expect(result1.result.length).toBeGreaterThan(0);
      expect(result2.result.length).toBeGreaterThan(0);
    }, TEST_TIMEOUT);
  });

  describe('Error Handling Tests', () => {
    test('should handle empty input correctly', async () => {
      const agentB = new AgentB();
      
      // 测试空输入
      await expect(agentB.execute('', 'test-session-error'))
        .rejects.toThrow('Input cannot be empty');
      
      // 测试null输入
      await expect(agentB.execute(null as any, 'test-session-error'))
        .rejects.toThrow('Input cannot be empty');
    });

    test('should handle workflow execution errors gracefully', async () => {
      const agentB = new AgentB();
      
      // 测试极端情况下的错误处理
      const extremeInput = 'x'.repeat(20000); // 超长输入
      
      try {
        await agentB.execute(extremeInput, 'test-session-extreme');
      } catch (error) {
        expect(error).toBeInstanceOf(AgentError);
      }
    });
  });

  describe('Performance and Reliability Tests', () => {
    test('should handle concurrent requests', async () => {
      const agentB = new AgentB();
      const testInputs = [
        '分析人工智能的发展',
        '评估云计算的优势',
        '探讨区块链的应用'
      ];
      
      const promises = testInputs.map((input, index) => 
        agentB.execute(input, `concurrent-session-${index}`)
      );
      
      const results = await Promise.all(promises);
      
      results.forEach((result, index) => {
        expect(result.success).toBe(true);
        expect(result.result).toBeDefined();
        expect(result.result.length).toBeGreaterThan(0);
      });
    }, TEST_TIMEOUT);

    test('should maintain consistent output format', async () => {
      const agentB = new AgentB();
      const testInputs = [
        '分析区块链技术的应用前景',
        '评估机器学习在医疗领域的潜力',
        '探讨物联网技术的发展趋势'
      ];
      
      for (let i = 0; i < testInputs.length; i++) {
        const result = await agentB.execute(testInputs[i], `test-session-format-${i}`);
        
        expect(result.success).toBe(true);
        expect(result.result).toBeDefined();
        expect(typeof result.result).toBe('string');
        expect(result.execution_time).toBeGreaterThan(0);
        expect(result.tokens_used).toBeGreaterThan(0);
      }
    }, TEST_TIMEOUT * 3);

    test('should pass health check', async () => {
      const agentB = new AgentB();
      
      const isHealthy = await agentB.healthCheck();
      expect(isHealthy).toBe(true);
    }, TEST_TIMEOUT);

    test('should return correct configuration', () => {
      const agentB = new AgentB();
      const config = agentB.getConfig();
      
      expect(config).toBeDefined();
      expect(config.name).toBe('Agent B');
      expect(config.description).toContain('第二个LLM智能体');
      expect(config.model).toBeDefined();
      expect(config.temperature).toBe(0.5);
      expect(config.maxTokens).toBe(1000);
      expect(config.supports_iteration).toBe(true);
      expect(config.max_iterations).toBe(3);
      expect(config.implementation).toBe('StateGraph');
      expect(config.workflow_nodes).toEqual(['initial_analysis', 'iterative_analysis', 'continue_check', 'finalize_result']);
    });
  });

  describe('Backward Compatibility Tests', () => {
    test('should maintain same interface as original Agent B', async () => {
      const agentB = new AgentB();
      const testInput = '测试向后兼容性';
      
      const result = await agentB.execute(testInput, 'test-session-compatibility');
      
      // 验证返回结果结构与原版本一致
      expect(result).toHaveProperty('success');
      expect(result).toHaveProperty('result');
      expect(result).toHaveProperty('execution_time');
      expect(result).toHaveProperty('tokens_used');
      
      expect(typeof result.success).toBe('boolean');
      expect(typeof result.result).toBe('string');
      expect(typeof result.execution_time).toBe('number');
      expect(typeof result.tokens_used).toBe('number');
    }, TEST_TIMEOUT);

    test('should support original execute method signature with iteration parameter', async () => {
      const agentB = new AgentB();
      const testInput = '测试迭代参数兼容性';
      
      // 测试原有的三参数调用方式
      const result = await agentB.execute(testInput, 'test-session-compat', 0);
      
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      expect(result.result.length).toBeGreaterThan(0);
    }, TEST_TIMEOUT);
  });

  describe('StateGraph Specific Tests', () => {
    test('should demonstrate iterative improvement capability', async () => {
      const agentB = new AgentB();
      const testInput = '这是一个需要深入分析的复杂问题，请提供详细的见解';
      
      const result = await agentB.execute(testInput, 'test-session-iteration');
      
      expect(result.success).toBe(true);
      expect(result.result).toBeDefined();
      
      // 验证结果包含迭代分析的特征
      const content = result.result;
      expect(content).toContain('分析');
      expect(content.length).toBeGreaterThan(100); // 确保有足够的内容
    }, TEST_TIMEOUT);
  });
});
