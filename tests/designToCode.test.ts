import { graph } from "../src/graph/designToCode";
import { DesignItem } from "../src/graph/designToCode/types";
import { SystemMessage } from "@langchain/core/messages";

// 测试超时设置
const TEST_TIMEOUT = 60000;

describe("DesignToCode Graph Tests", () => {
  // 模拟设计稿数据
  const mockDesignItems: DesignItem[] = [
    {
      pageName: "首页",
      pageContent: "这是一个简单的首页设计，包含导航栏、轮播图和产品展示区域",
      type: "html",
    },
    {
      pageName: "产品页",
      pageContent: "产品详情页面，包含产品图片、描述、价格和购买按钮",
      type: "html",
    },
  ];

  const mockImageDesignItems: DesignItem[] = [
    {
      pageName: "登录页",
      pageContent:
        "base64:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      type: "img",
    },
  ];

  describe("Graph Structure Tests", () => {
    test("graph should be properly compiled", () => {
      expect(graph).toBeDefined();
      expect(graph.name).toBe("designToCode");
      expect(typeof graph.invoke).toBe("function");
    });

    test("graph should have correct structure", () => {
      expect(graph).toBeDefined();
      expect(typeof graph.invoke).toBe("function");
      expect(graph.name).toBe("designToCode");
    });
  });

  describe("Input Validation Tests", () => {
    test(
      "should handle empty input array",
      async () => {
        const result = await graph.invoke({
          input: [],
        });

        expect(result).toBeDefined();
        expect(result.error).toBeDefined();
        expect(result.error).toContain("没有找到有效的设计稿");
        expect(result.output).toBeDefined();
        expect(result.messages).toBeDefined();
        expect(result.messages.length).toBeGreaterThan(0);
      },
      TEST_TIMEOUT
    );

    test(
      "should handle null input",
      async () => {
        const result = await graph.invoke({
          input: null as any,
        });

        expect(result).toBeDefined();
        expect(result.error).toBeDefined();
        expect(result.output).toBeDefined();
      },
      TEST_TIMEOUT
    );

    test(
      "should handle undefined input",
      async () => {
        const result = await graph.invoke({
          input: undefined as any,
        });

        expect(result).toBeDefined();
        expect(result.error).toBeDefined();
        expect(result.output).toBeDefined();
      },
      TEST_TIMEOUT
    );
  });

  describe("Valid Input Processing Tests", () => {
    test(
      "should process valid HTML design items",
      async () => {
        const result = await graph.invoke({
          input: mockDesignItems,
        });

        expect(result).toBeDefined();
        expect(result.input).toEqual(mockDesignItems);
        expect(result.htmlResults).toBeDefined();
        expect(Array.isArray(result.htmlResults)).toBe(true);
        expect(result.htmlResults.length).toBe(mockDesignItems.length);

        // 验证每个结果的结构
        result.htmlResults.forEach((item, index) => {
          expect(item.designIndex).toBe(index);
          expect(item.designPageName).toBe(mockDesignItems[index].pageName);
          expect(typeof item.html).toBe("string");
        });

        // 验证最终输出
        expect(result.output).toBeDefined();
        expect(typeof result.output).toBe("string");
        expect(result.output.length).toBeGreaterThan(0);
      },
      TEST_TIMEOUT
    );

    test(
      "should process image design items",
      async () => {
        const result = await graph.invoke({
          input: mockImageDesignItems,
        });

        expect(result).toBeDefined();
        expect(result.input).toEqual(mockImageDesignItems);
        expect(result.htmlResults).toBeDefined();
        expect(Array.isArray(result.htmlResults)).toBe(true);
        expect(result.htmlResults.length).toBe(mockImageDesignItems.length);

        // 验证图片处理结果
        result.htmlResults.forEach((item, index) => {
          expect(item.designIndex).toBe(index);
          expect(item.designPageName).toBe(
            mockImageDesignItems[index].pageName
          );
          expect(typeof item.html).toBe("string");
        });
      },
      TEST_TIMEOUT
    );

    test(
      "should generate combined HTML",
      async () => {
        const result = await graph.invoke({
          input: mockDesignItems,
        });

        expect(result).toBeDefined();
        expect(result.combinedHtml).toBeDefined();
        expect(typeof result.combinedHtml).toBe("string");
        expect(result.combinedHtml.length).toBeGreaterThan(0);
      },
      TEST_TIMEOUT
    );

    test(
      "should generate project code",
      async () => {
        const result = await graph.invoke({
          input: mockDesignItems,
        });

        expect(result).toBeDefined();
        expect(result.projectCode).toBeDefined();
        expect(typeof result.projectCode).toBe("string");
        expect(result.projectCode.length).toBeGreaterThan(0);
      },
      TEST_TIMEOUT
    );
  });

  describe("Message Flow Tests", () => {
    test(
      "should generate appropriate system messages",
      async () => {
        const result = await graph.invoke({
          input: mockDesignItems,
        });

        expect(result.messages).toBeDefined();
        expect(Array.isArray(result.messages)).toBe(true);
        expect(result.messages.length).toBeGreaterThan(0);

        // 验证消息类型
        result.messages.forEach((message) => {
          expect(message).toBeInstanceOf(SystemMessage);
          expect(typeof message.content).toBe("string");
          expect(message.content.length).toBeGreaterThan(0);
        });
      },
      TEST_TIMEOUT
    );

    test(
      "should include processing stage messages",
      async () => {
        const result = await graph.invoke({
          input: mockDesignItems,
        });

        const messageContents = result.messages.map((msg) => msg.content);

        // 验证是否包含关键处理阶段的消息
        expect(
          messageContents.some(
            (content) =>
              content.includes("并行处理完成") ||
              content.includes("HTML 合并完成") ||
              content.includes("项目代码生成完成")
          )
        ).toBe(true);
      },
      TEST_TIMEOUT
    );
  });

  describe("Error Handling Tests", () => {
    test(
      "should handle malformed design items",
      async () => {
        const malformedItems = [
          {
            pageName: "测试页",
            // 缺少 pageContent 和 type
          } as any,
        ];

        const result = await graph.invoke({
          input: malformedItems,
        });

        expect(result).toBeDefined();
        // 即使输入有问题，也应该有某种输出或错误处理
        expect(result.output).toBeDefined();
      },
      TEST_TIMEOUT
    );

    test(
      "should handle large number of design items",
      async () => {
        const largeDesignItems: DesignItem[] = Array.from(
          { length: 5 },
          (_, i) => ({
            pageName: `页面${i + 1}`,
            pageContent: `这是第${i + 1}个页面的设计内容`,
            type: "html" as const,
          })
        );

        const result = await graph.invoke({
          input: largeDesignItems,
        });

        expect(result).toBeDefined();
        expect(result.htmlResults).toBeDefined();
        expect(result.htmlResults.length).toBe(largeDesignItems.length);
        expect(result.output).toBeDefined();
      },
      TEST_TIMEOUT
    );
  });

  describe("State Management Tests", () => {
    test(
      "should maintain input immutability",
      async () => {
        const originalInput = [...mockDesignItems];
        const result = await graph.invoke({
          input: mockDesignItems,
        });

        // 验证输入没有被修改
        expect(result.input).toEqual(originalInput);
        expect(mockDesignItems).toEqual(originalInput);
      },
      TEST_TIMEOUT
    );

    test(
      "should properly handle state transitions",
      async () => {
        const result = await graph.invoke({
          input: mockDesignItems,
        });

        // 验证状态转换的完整性
        expect(result.input).toBeDefined();
        expect(result.htmlResults).toBeDefined();
        expect(result.combinedHtml).toBeDefined();
        expect(result.projectCode).toBeDefined();
        expect(result.output).toBeDefined();

        // 验证中间结果的数量
        expect(result.htmlResults.length).toBe(mockDesignItems.length);
      },
      TEST_TIMEOUT
    );
  });

  describe("Performance Tests", () => {
    test(
      "should complete processing within reasonable time",
      async () => {
        const startTime = Date.now();

        const result = await graph.invoke({
          input: mockDesignItems,
        });

        const endTime = Date.now();
        const processingTime = endTime - startTime;

        expect(result).toBeDefined();
        expect(result.output).toBeDefined();
        expect(processingTime).toBeLessThan(30000); // 30秒内完成
      },
      TEST_TIMEOUT
    );
  });

  describe("Integration Tests", () => {
    test(
      "should work with real-world design scenarios",
      async () => {
        const realisticDesignItems: DesignItem[] = [
          {
            pageName: "Landing Page",
            pageContent: `
            <div class="hero-section">
              <h1>Welcome to Our Platform</h1>
              <p>Discover amazing features and solutions</p>
              <button class="cta-button">Get Started</button>
            </div>
            <div class="features-section">
              <div class="feature-card">
                <h3>Feature 1</h3>
                <p>Description of feature 1</p>
              </div>
              <div class="feature-card">
                <h3>Feature 2</h3>
                <p>Description of feature 2</p>
              </div>
            </div>
          `,
            type: "html",
          },
          {
            pageName: "Dashboard",
            pageContent: `
            <div class="dashboard-layout">
              <nav class="sidebar">
                <ul>
                  <li><a href="#overview">Overview</a></li>
                  <li><a href="#analytics">Analytics</a></li>
                  <li><a href="#settings">Settings</a></li>
                </ul>
              </nav>
              <main class="content">
                <div class="widget-grid">
                  <div class="widget">Widget 1</div>
                  <div class="widget">Widget 2</div>
                  <div class="widget">Widget 3</div>
                </div>
              </main>
            </div>
          `,
            type: "html",
          },
        ];

        const result = await graph.invoke({
          input: realisticDesignItems,
        });

        expect(result).toBeDefined();
        expect(result.input).toEqual(realisticDesignItems);
        expect(result.htmlResults).toBeDefined();
        expect(result.htmlResults.length).toBe(realisticDesignItems.length);
        expect(result.combinedHtml).toBeDefined();
        expect(result.projectCode).toBeDefined();
        expect(result.output).toBeDefined();

        // 验证生成的HTML包含原始内容的关键元素
        expect(result.combinedHtml).toContain("hero-section");
        expect(result.combinedHtml).toContain("dashboard-layout");
        expect(result.combinedHtml).toContain("sidebar");
      },
      TEST_TIMEOUT
    );
  });
});
