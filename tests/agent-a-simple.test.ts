import { AgentA } from '../src/agents/agent-a';

describe('Agent A StateGraph Simple Test', () => {
  const TEST_TIMEOUT = 30000; // 30秒超时

  test('should initialize with StateGraph successfully', () => {
    const agentA = new AgentA();
    
    // 验证Agent A实例化成功
    expect(agentA).toBeDefined();
    expect(agentA.getConfig()).toBeDefined();
    expect(agentA.getConfig().name).toBe('Agent A');
  });

  test('should execute simple analysis using StateGraph', async () => {
    const agentA = new AgentA();
    const testInput = '请分析人工智能的发展趋势';
    
    const result = await agentA.execute(testInput, 'test-session-simple');
    
    // 验证基本结果结构
    expect(result.success).toBe(true);
    expect(result.result).toBeDefined();
    expect(typeof result.result).toBe('string');
    expect(result.result.length).toBeGreaterThan(0);
    expect(result.execution_time).toBeGreaterThan(0);
    
    // 验证结果内容质量
    expect(result.result).toContain('人工智能');
    expect(result.result.length).toBeGreaterThan(50); // 确保有足够的分析内容
    
    console.log('✅ StateGraph execution successful!');
    console.log('Result length:', result.result.length);
    console.log('Execution time:', result.execution_time, 'ms');
  }, TEST_TIMEOUT);

  test('should handle error cases correctly', async () => {
    const agentA = new AgentA();
    
    // 测试空输入
    await expect(agentA.execute('', 'test-session-error'))
      .rejects.toThrow('Input cannot be empty');
    
    console.log('✅ Error handling working correctly!');
  });

  test('should pass health check', async () => {
    const agentA = new AgentA();
    
    const isHealthy = await agentA.healthCheck();
    expect(isHealthy).toBe(true);
    
    console.log('✅ Health check passed!');
  }, TEST_TIMEOUT);
});
