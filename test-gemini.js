// 测试 Gemini 模型
import { ChatGoogleGenerativeAI } from "@langchain/google-genai";
import { HumanMessage, SystemMessage } from "@langchain/core/messages";

async function testGemini() {
  try {
    // 创建 Gemini 模型实例
    const model = new ChatGoogleGenerativeAI({
      model: "gemini-2.0-flash",
      temperature: 0,
      apiKey: process.env.GOOGLE_API_KEY || "AIzaSyAkAltrN_VvzDaht6OWJJQJ-5ZxKOCSr9k",
    });

    console.log("正在测试 Gemini 模型...");

    // 测试简单对话
    const response = await model.invoke([
      new SystemMessage("你是一个有用的AI助手。"),
      new HumanMessage("你好！请简单介绍一下你自己。")
    ]);

    console.log("Gemini 响应:", response.content);
    console.log("测试成功！");

  } catch (error) {
    console.error("测试失败:", error.message);
  }
}

// 运行测试
testGemini(); 