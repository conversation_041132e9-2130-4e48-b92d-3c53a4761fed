# API配置
OPENAI_API_KEY=sk-cITab2RRLmsfbUH-pNJg
OPENAI_API_BASE=http://168.63.85.222/web/unauth/LLM_api_proxy/v1
OPENAI_MODEL_NAME=ht::saas-deepseek-v3

# Google API配置 (用于 Gemini 模型)
GOOGLE_API_KEY=your-google-api-key-here
GOOGLE_BASE_URL=https://generativelanguage.googleapis.com

# MongoDB配置
# 本地环境
MONGODB_URI=****************************************************
MONGODB_DB_NAME=haicode_cli
# 测试环境
# MONGODB_URI=*********************************************************************
# MONGODB_DB_NAME=webdp

# 服务器配置
PORT=3000
NODE_ENV=development

# 日志配置
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# 缓存配置
# REDIS_URL=redis://localhost:6379

# 安全配置
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here
