import { Client } from "@langchain/langgraph-sdk";

async function testThreads() {
  const client = new Client({ apiUrl: "http://localhost:2024" });
  // Using the graph deployed with the name "agent"
  const assistantID = "designToCode";
  // create thread
  const thread = await client.threads.create();
  console.log(thread);
}

testThreads().catch((error) => {
  console.error("❌ 测试运行失败:", error);
  process.exit(1);
});
