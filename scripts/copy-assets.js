#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { config } from './copy-assets.config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

/**
 * 检查文件是否匹配忽略模式
 * @param {string} filename 文件名
 * @returns {boolean} 是否应该忽略
 */
function shouldIgnoreFile(filename) {
  return config.ignorePatterns.some(pattern => {
    // 简单的通配符匹配
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(filename);
    }
    return filename === pattern;
  });
}

/**
 * 递归扫描目录，找到所有需要复制的资源文件
 * @param {string} srcDir 源目录
 * @param {string} baseDir 基础目录（用于计算相对路径）
 * @returns {Array<{src: string, dest: string}>} 文件路径数组
 */
function scanAssets(srcDir, baseDir = srcDir) {
  const assets = [];
  
  try {
    const items = fs.readdirSync(srcDir);
    
    for (const item of items) {
      const fullPath = path.join(srcDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 跳过忽略的目录
        if (config.ignoreDirs.includes(item)) {
          if (config.verbose) {
            console.log(`⏭️  Skipping ignored directory: ${path.relative(projectRoot, fullPath)}`);
          }
          continue;
        }
        // 递归扫描子目录
        assets.push(...scanAssets(fullPath, baseDir));
      } else if (stat.isFile()) {
        // 检查是否应该忽略此文件
        if (shouldIgnoreFile(item)) {
          if (config.verbose) {
            console.log(`⏭️  Skipping ignored file: ${path.relative(projectRoot, fullPath)}`);
          }
          continue;
        }
        
        // 检查是否是资源文件
        const ext = path.extname(item).toLowerCase();
        if (config.assetExtensions.includes(ext)) {
          const relativePath = path.relative(baseDir, fullPath);
          const destPath = path.join(projectRoot, config.outputRoot, relativePath);
          assets.push({
            src: fullPath,
            dest: destPath
          });
        }
      }
    }
  } catch (error) {
    console.warn(`⚠️  Warning: Could not scan directory ${srcDir}:`, error.message);
  }
  
  return assets;
}

/**
 * 复制文件
 * @param {string} src 源文件路径
 * @param {string} dest 目标文件路径
 */
function copyFile(src, dest) {
  try {
    // 确保目标目录存在
    const destDir = path.dirname(dest);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }
    
    // 复制文件
    fs.copyFileSync(src, dest);
  } catch (error) {
    console.error(`❌ Failed to copy ${src}:`, error.message);
  }
}

/**
 * 清理目标目录中的旧文件
 * @param {Array<{src: string, dest: string}>} assets 当前要复制的资源列表
 */
function cleanOldFiles(assets) {
  if (!config.cleanBeforeCopy) {
    return;
  }
  
  const outputDir = path.join(projectRoot, config.outputRoot);
  if (!fs.existsSync(outputDir)) {
    return;
  }
  
  const currentDestFiles = new Set(assets.map(asset => asset.dest));
  
  function cleanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          cleanDirectory(fullPath);
          // 如果目录为空，删除它
          if (fs.readdirSync(fullPath).length === 0) {
            fs.rmdirSync(fullPath);
          }
        } else if (stat.isFile()) {
          // 检查是否是当前要复制的文件
          if (!currentDestFiles.has(fullPath)) {
            // 检查是否是资源文件
            const ext = path.extname(item).toLowerCase();
            if (config.assetExtensions.includes(ext)) {
              fs.unlinkSync(fullPath);
              console.log(`🗑️  Removed old file: ${path.relative(projectRoot, fullPath)}`);
            }
          }
        }
      }
    } catch (error) {
      console.warn(`⚠️  Warning: Could not clean directory ${dir}:`, error.message);
    }
  }
  
  cleanDirectory(outputDir);
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 Starting asset copy process...');
  console.log(`📁 Scanning directory: ${config.scanRoot}`);
  console.log(`📦 Output directory: ${config.outputRoot}`);
  
  const srcDir = path.join(projectRoot, config.scanRoot);
  
  if (!fs.existsSync(srcDir)) {
    console.error(`❌ Source directory not found: ${srcDir}`);
    process.exit(1);
  }
  
  // 扫描所有资源文件
  const assets = scanAssets(srcDir);
  
  if (assets.length === 0) {
    console.log('ℹ️  No assets found to copy');
    return;
  }
  
  console.log(`📁 Found ${assets.length} asset(s) to copy:`);
  
  // 清理旧文件（如果启用）
  if (config.cleanBeforeCopy) {
    console.log('🧹 Cleaning old files...');
    cleanOldFiles(assets);
  }
  
  // 复制所有资源文件
  let successCount = 0;
  for (const asset of assets) {
    copyFile(asset.src, asset.dest);
    successCount++;
  }
  
  console.log(`\n✅ Asset copy completed: ${successCount}/${assets.length} files copied successfully`);
}

// 运行主函数
main();
