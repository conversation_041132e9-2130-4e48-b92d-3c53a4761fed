<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Document</title>
    <style>
      body {
        margin: 0;
      }
      ::-webkit-scrollbar {
        display: none;
      }
      button {
        margin: 0;
        padding: 0;
        border: 1px solid transparent;
        outline: 0;
      }
      button:active {
        opacity: 0.6;
      }
      .container {
        position: relative;
        width: 375px;
        height: 812px;
        background-color: #eef0f3;
        overflow: hidden;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }
      .layer1 {
        width: 375px;
        height: 812px;
        display: flex;
        flex-direction: column;
      }
      .mod1 {
        height: 44px;
        align-self: center;
        width: 375px;
        justify-content: flex-end;
        padding-bottom: 12px;
        align-items: flex-end;
        padding-right: 14px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .mod2 {
        width: 340px;
        height: 16px;
        flex-direction: row;
        display: flex;
      }
      .wrap1 {
        width: 54px;
        height: 16px;
        overflow-wrap: break-word;
        text-align: center;
        box-sizing: border-box;
        font-size: 0;
      }
      .word1 {
        font-size: 14px;
        font-family: SFProText-Semibold;
        color: #fff;
        line-height: 16px;
      }
      .word2 {
        font-size: 14px;
        font-family: SFProText-Semibold;
        color: #fff;
        line-height: 16px;
      }
      .wrap2 {
        width: 17px;
        height: 11px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch8ce7767a969f0a87db754c9e45a3e7a4dcead05688664c56b6305e19b5138508.png);
        background-repeat: no-repeat;
        background-position: -0.6666666666660603px -0.6666666666666288px;
        margin-top: 2px;
        margin-left: 219px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .wrap3 {
        width: 16px;
        height: 11px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch92d71d13da58ba4887551315bf83a1d7044dd8e56d5d54f8f479185caa02c6e2.png);
        background-repeat: no-repeat;
        background-position: -0.6937274976498884px -0.3306727040325086px;
        margin-top: 2px;
        margin-left: 5px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .label1 {
        width: 25px;
        height: 12px;
        margin-top: 1px;
        margin-left: 4px;
      }
      .mod3 {
        height: 50px;
        align-self: center;
        width: 375px;
        justify-content: center;
        align-items: center;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .mod4 {
        width: 335px;
        height: 42px;
        flex-direction: row;
        display: flex;
      }
      .label2 {
        width: 30px;
        height: 30px;
        margin-top: 6px;
      }
      .bd1 {
        position: relative;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        overflow: hidden;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch1c79f5ba942235a6c9a96044ee18649db6f54bec2a4ea78b46ab5f88d3e569cb.png);
        background-repeat: no-repeat;
        background-size: 100%;
        margin-top: 25px;
        margin-left: 64px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .section1 {
        position: absolute;
        left: 1px;
        top: 3px;
        width: 15px;
        height: 15px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketche96de86f6f0621ce3d60cfb7e0637a5f7eb6e589df5f7c4085512dd37e29f90a.png);
        background-repeat: no-repeat;
        background-position: -0.8888888888889142px -0.07407407407413302px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .bd2 {
        width: 115px;
        height: 42px;
        margin-left: 3px;
        display: flex;
        flex-direction: column;
      }
      .word3 {
        width: 68px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 17px;
        text-transform: uppercase;
        font-family: PingFangSC-Medium;
        line-height: 24px;
        text-align: left;
        align-self: flex-start;
        margin-left: 21px;
      }
      .main1 {
        width: 115px;
        height: 18px;
        overflow-wrap: break-word;
        text-align: left;
        align-self: center;
        box-sizing: border-box;
        font-size: 0;
      }
      .word4 {
        font-size: 13px;
        font-family: PingFangSC-Regular;
        color: #030303;
        line-height: 18px;
      }
      .txt1 {
        font-size: 13px;
        font-family: ZLCaiFuTi-Regular;
        color: #030303;
        line-height: 18px;
      }
      .label3 {
        width: 11px;
        height: 11px;
        margin-top: 28px;
        margin-left: 2px;
      }
      .img1 {
        width: 60px;
        height: 30px;
        margin-top: 6px;
        margin-left: 34px;
      }
      .mod5 {
        width: 236px;
        height: 22px;
        margin-left: 20px;
        margin-top: 10px;
        flex-direction: row;
        display: flex;
        justify-content: space-between;
      }
      .txt2 {
        width: 32px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 16px;
        font-family: PingFangSC-Medium;
        line-height: 22px;
        text-align: left;
      }
      .word5 {
        width: 32px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 16px;
        font-family: PingFangSC-Regular;
        line-height: 22px;
        text-align: left;
      }
      .word6 {
        width: 32px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 16px;
        font-family: PingFangSC-Regular;
        line-height: 22px;
        text-align: left;
      }
      .info1 {
        width: 80px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 16px;
        font-family: PingFangSC-Regular;
        line-height: 22px;
        text-align: left;
      }
      .mod6 {
        width: 8px;
        height: 3px;
        border-radius: 1.5px 1.5px 1.5px 1.5px;
        background-color: #030303;
        align-self: flex-start;
        margin-left: 32px;
        margin-top: 2px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .mod7 {
        width: 335px;
        height: 20px;
        margin-left: 20px;
        margin-top: 20px;
        flex-direction: row;
        display: flex;
      }
      .word7 {
        width: 56px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        line-height: 20px;
        text-align: left;
      }
      .layer2 {
        height: 19px;
        border-radius: 4px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch15e4a3055c64c69dcaf9fec864bf3ebefdc2ca759e7739388f35bf2bac7638b1.png);
        background-repeat: no-repeat;
        background-position: -1px -1px;
        margin-left: 6px;
        width: 62px;
        justify-content: center;
        align-items: center;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .main2 {
        width: 51px;
        height: 14px;
        flex-direction: row;
        display: flex;
        justify-content: space-between;
      }
      .section2 {
        height: 11px;
        border-radius: 50%;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch80578d098606722ad71d6b3dc79cf834aeab56ff2a2200215d264044c8ecf15b.png);
        background-repeat: no-repeat;
        background-size: 100%;
        margin-top: 2px;
        width: 11px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .outer1 {
        height: 11px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchb3f7bef1f0ca400868a6c453ab603ae1918cb82828de3527b9bdb2e9b5932d2a.png);
        background-repeat: no-repeat;
        background-size: 100%;
        width: 11px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .label4 {
        width: 11px;
        height: 11px;
      }
      .word8 {
        width: 23px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: left;
      }
      .icon1 {
        width: 11px;
        height: 11px;
        margin-top: 2px;
      }
      .txt3 {
        width: 84px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        line-height: 20px;
        text-align: left;
        margin-left: 127px;
      }
      .mod8 {
        width: 335px;
        height: 22px;
        margin-left: 20px;
        margin-top: 6px;
        flex-direction: row;
        display: flex;
        justify-content: space-between;
      }
      .info2 {
        width: 111px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 18px;
        font-family: ZLCaiFuTi-Bold;
        line-height: 26px;
        text-align: left;
      }
      .txt4 {
        width: 120px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 18px;
        font-family: ZLCaiFuTi-Bold;
        line-height: 26px;
        text-align: left;
      }
      .mod9 {
        height: 595px;
        border-radius: 20px 20px 0 0;
        background-color: #fff;
        align-self: center;
        margin-top: 18px;
        width: 375px;
        justify-content: flex-end;
        padding-bottom: 9px;
        align-items: center;
        position: relative;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .mod10 {
        width: 335px;
        height: 562px;
        display: flex;
        flex-direction: column;
      }
      .bd3 {
        width: 335px;
        height: 22px;
        flex-direction: row;
        display: flex;
      }
      .info3 {
        width: 64px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 16px;
        font-family: PingFangSC-Medium;
        line-height: 22px;
        text-align: left;
      }
      .txt5 {
        width: 92px;
        display: block;
        overflow-wrap: break-word;
        color: #a6acb8;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: left;
        margin-top: 3px;
        margin-left: 4px;
      }
      .word9 {
        width: 28px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        line-height: 20px;
        text-align: left;
        margin-top: 1px;
        margin-left: 133px;
      }
      .label5 {
        width: 14px;
        height: 14px;
        margin-top: 4px;
      }
      .bd4 {
        width: 335px;
        height: 18px;
        margin-top: 18px;
        flex-direction: row;
        display: flex;
      }
      .info4 {
        width: 59px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 13px;
        font-family: PingFangSC-Regular;
        line-height: 18px;
        text-align: left;
      }
      .word10 {
        width: 59px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 13px;
        font-family: PingFangSC-Regular;
        line-height: 18px;
        text-align: right;
        margin-left: 97px;
      }
      .info5 {
        width: 52px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 13px;
        font-family: PingFangSC-Regular;
        line-height: 18px;
        text-align: right;
        margin-left: 68px;
      }
      .bd5 {
        height: 72px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchf93e8932ddb74824b648aa5f4f1a0f9b109747be059219ad5384b88eee1744b1.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .box1 {
        width: 335px;
        height: 21px;
        margin-top: 15px;
        flex-direction: row;
        display: flex;
      }
      .word11 {
        width: 60px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .word12 {
        width: 61px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 94px;
      }
      .info6 {
        width: 57px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 63px;
      }
      .box2 {
        width: 335px;
        height: 14px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .label6 {
        width: 18px;
        height: 12px;
        margin-top: 2px;
      }
      .word13 {
        width: 36px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: justify;
        margin-left: 3px;
      }
      .word14 {
        width: 29px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 129px;
      }
      .info7 {
        width: 42px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 78px;
      }
      .bd6 {
        height: 72px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchf93e8932ddb74824b648aa5f4f1a0f9b109747be059219ad5384b88eee1744b1.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .group1 {
        width: 335px;
        height: 21px;
        margin-top: 15px;
        flex-direction: row;
        display: flex;
      }
      .word15 {
        width: 75px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .txt6 {
        width: 59px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 81px;
      }
      .info8 {
        width: 70px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 50px;
      }
      .group2 {
        width: 335px;
        height: 14px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .icon2 {
        width: 18px;
        height: 12px;
        margin-top: 2px;
      }
      .word16 {
        width: 34px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: justify;
        margin-left: 3px;
      }
      .word17 {
        width: 28px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 132px;
      }
      .word18 {
        width: 49px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 71px;
      }
      .bd7 {
        height: 72px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchf93e8932ddb74824b648aa5f4f1a0f9b109747be059219ad5384b88eee1744b1.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .mod11 {
        width: 335px;
        height: 21px;
        margin-top: 15px;
        flex-direction: row;
        display: flex;
      }
      .word19 {
        width: 84px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .word20 {
        width: 49px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 80px;
      }
      .word21 {
        width: 71px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 51px;
      }
      .mod12 {
        width: 335px;
        height: 14px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .icon3 {
        width: 18px;
        height: 12px;
        margin-top: 2px;
      }
      .word22 {
        width: 36px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: justify;
        margin-left: 3px;
      }
      .txt7 {
        width: 28px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 128px;
      }
      .word23 {
        width: 50px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 72px;
      }
      .bd8 {
        height: 72px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchf93e8932ddb74824b648aa5f4f1a0f9b109747be059219ad5384b88eee1744b1.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .layer3 {
        width: 335px;
        height: 21px;
        margin-top: 15px;
        flex-direction: row;
        display: flex;
      }
      .word24 {
        width: 30px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .info9 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 135px;
      }
      .word25 {
        width: 58px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 64px;
      }
      .layer4 {
        width: 335px;
        height: 14px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .icon4 {
        width: 18px;
        height: 12px;
        margin-top: 2px;
      }
      .word26 {
        width: 36px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: justify;
        margin-left: 3px;
      }
      .word27 {
        width: 28px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 128px;
      }
      .info10 {
        width: 50px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 72px;
      }
      .bd9 {
        height: 72px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchf93e8932ddb74824b648aa5f4f1a0f9b109747be059219ad5384b88eee1744b1.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .wrap4 {
        width: 335px;
        height: 21px;
        margin-top: 15px;
        flex-direction: row;
        display: flex;
      }
      .word28 {
        width: 60px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .word29 {
        width: 49px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 104px;
      }
      .info11 {
        width: 58px;
        display: block;
        overflow-wrap: break-word;
        color: #05b063;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 64px;
      }
      .wrap5 {
        width: 335px;
        height: 14px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .label7 {
        width: 18px;
        height: 12px;
        margin-top: 2px;
      }
      .txt8 {
        width: 36px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: justify;
        margin-left: 3px;
      }
      .word30 {
        width: 22px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 134px;
      }
      .info12 {
        width: 49px;
        display: block;
        overflow-wrap: break-word;
        color: #05b063;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 73px;
      }
      .bd10 {
        height: 72px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchf93e8932ddb74824b648aa5f4f1a0f9b109747be059219ad5384b88eee1744b1.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .wrap6 {
        width: 335px;
        height: 21px;
        margin-top: 15px;
        flex-direction: row;
        display: flex;
      }
      .word31 {
        width: 60px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .word32 {
        width: 49px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 104px;
      }
      .word33 {
        width: 58px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 64px;
      }
      .wrap7 {
        width: 335px;
        height: 14px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .label8 {
        width: 18px;
        height: 12px;
        margin-top: 2px;
      }
      .word34 {
        width: 36px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: justify;
        margin-left: 3px;
      }
      .word35 {
        width: 21px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 135px;
      }
      .word36 {
        width: 42px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 80px;
      }
      .bd11 {
        height: 72px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchf93e8932ddb74824b648aa5f4f1a0f9b109747be059219ad5384b88eee1744b1.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .box3 {
        width: 335px;
        height: 21px;
        margin-top: 15px;
        flex-direction: row;
        display: flex;
      }
      .word37 {
        width: 60px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .txt9 {
        width: 49px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 104px;
      }
      .info13 {
        width: 58px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-top: 1px;
        margin-left: 64px;
      }
      .box4 {
        width: 335px;
        height: 14px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .icon5 {
        width: 18px;
        height: 12px;
        margin-top: 2px;
      }
      .info14 {
        width: 36px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: justify;
        margin-left: 3px;
      }
      .word38 {
        width: 21px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 135px;
      }
      .word39 {
        width: 42px;
        display: block;
        overflow-wrap: break-word;
        color: #f32e2d;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: right;
        margin-left: 80px;
      }
      .mod13 {
        position: absolute;
        left: 121px;
        top: 582px;
        width: 134px;
        height: 5px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch39e0145cb5f9aedbdc47ba7b97079bb91f0227f31b11f019bb74d1d676930079.png);
        background-repeat: no-repeat;
        background-position: -0.5px 0;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="layer1">
        <div class="mod1">
          <div class="mod2">
            <div class="wrap1">
              <span class="word1">9:4</span> <span class="word2">1</span>
            </div>
            <div class="wrap2"></div>
            <div class="wrap3"></div>
            <img
              class="label1"
              referrerpolicy="no-referrer"
              src="http://lanhu.htsc.com.cn:8089/images/sketch7c371ddedb0f192c948d81e33b4f6db6925d7eab4e2172f54a9c0421233ea5bd.png"
            />
          </div>
        </div>
        <div class="mod3">
          <div class="mod4">
            <img
              class="label2"
              referrerpolicy="no-referrer"
              src="http://lanhu.htsc.com.cn:8089/images/sketchc5ac05f5f3b629b6a0b0528b7184b03805656947bcee56c097c55e8285df3357.png"
            />
            <div class="bd1"><div class="section1"></div></div>
            <div class="bd2">
              <span class="word3">我的持仓</span>
              <div class="main1">
                <span class="word4">华泰国际</span>
                <span class="txt1">66***332</span>
              </div>
            </div>
            <img
              class="label3"
              referrerpolicy="no-referrer"
              src="http://lanhu.htsc.com.cn:8089/images/sketchf51161fb92cb249c793628d80811d0315c41b04d20c43523a6f2504fdc0db7a4.png"
            />
            <img
              class="img1"
              referrerpolicy="no-referrer"
              src="http://lanhu.htsc.com.cn:8089/images/sketch381ed0e136467827a8743d7db6389ce5e682c8bed3dcce271aee9b3e4e9365f1.png"
            />
          </div>
        </div>
        <div class="mod5">
          <span class="txt2">股票</span> <span class="word5">基金</span>
          <span class="word6">债券</span> <span class="info1">结构化产品</span>
        </div>
        <div class="mod6"></div>
        <div class="mod7">
          <span class="word7">持仓总值</span>
          <div class="layer2">
            <div class="main2">
              <div class="section2">
                <div class="outer1">
                  <img
                    class="label4"
                    referrerpolicy="no-referrer"
                    src="http://lanhu.htsc.com.cn:8089/images/sketch4cc290c939530284b98b6349b54ffcf38f04ffbf6b08391356611532fd153598.png"
                  />
                </div>
              </div>
              <span class="word8">HKD</span>
              <img
                class="icon1"
                referrerpolicy="no-referrer"
                src="http://lanhu.htsc.com.cn:8089/images/sketchf67c3d1f46dae1f9a3900efd76b856bc2573e14e81562ea10ac9a6d42777c9bb.png"
              />
            </div>
          </div>
          <span class="txt3">累计市值变动</span>
        </div>
        <div class="mod8">
          <span class="info2">8,653,240.44</span>
          <span class="txt4">+2,326,918.22</span>
        </div>
        <div class="mod9">
          <div class="mod10">
            <div class="bd3">
              <span class="info3">全部持仓</span>
              <span class="txt5">(单位为结算币种)</span>
              <span class="word9">筛选</span>
              <img
                class="label5"
                referrerpolicy="no-referrer"
                src="http://lanhu.htsc.com.cn:8089/images/sketch41530ed8bb9d2e49af9a8d18362f9f6bc05ec079fb7faa0b968a19a80430b52e.png"
              />
            </div>
            <div class="bd4">
              <span class="info4">名称/代码</span>
              <span class="word10">市值/数量</span>
              <span class="info5">市值变动</span>
            </div>
            <div class="bd5">
              <div class="box1">
                <span class="word11">腾讯控股</span>
                <span class="word12">3,356.55</span>
                <span class="info6">+341.34</span>
              </div>
              <div class="box2">
                <img
                  class="label6"
                  referrerpolicy="no-referrer"
                  src="http://lanhu.htsc.com.cn:8089/images/sketchcb46c72fe6d956701754f6d63df3c87c934b4e02c8e7f67b5f1b0c5f91754acf.png"
                />
                <span class="word13">00700</span>
                <span class="word14">2000</span>
                <span class="info7">+2.37%</span>
              </div>
            </div>
            <div class="bd6">
              <div class="group1">
                <span class="word15">比亚迪股份</span>
                <span class="txt6">1,025.10</span>
                <span class="info8">+4,034.16</span>
              </div>
              <div class="group2">
                <img
                  class="icon2"
                  referrerpolicy="no-referrer"
                  src="http://lanhu.htsc.com.cn:8089/images/sketchcb46c72fe6d956701754f6d63df3c87c934b4e02c8e7f67b5f1b0c5f91754acf.png"
                />
                <span class="word16">01211</span>
                <span class="word17">1800</span>
                <span class="word18">+28.13%</span>
              </div>
            </div>
            <div class="bd7">
              <div class="mod11">
                <span class="word19">阿里巴巴-W</span>
                <span class="word20">974.35</span>
                <span class="word21">+9,965.50</span>
              </div>
              <div class="mod12">
                <img
                  class="icon3"
                  referrerpolicy="no-referrer"
                  src="http://lanhu.htsc.com.cn:8089/images/sketchcb46c72fe6d956701754f6d63df3c87c934b4e02c8e7f67b5f1b0c5f91754acf.png"
                />
                <span class="word22">09988</span>
                <span class="txt7">1200</span>
                <span class="word23">+69.49%</span>
              </div>
            </div>
            <div class="bd8">
              <div class="layer3">
                <span class="word24">锅圈</span>
                <span class="info9">674.12</span>
                <span class="word25">+965.50</span>
              </div>
              <div class="layer4">
                <img
                  class="icon4"
                  referrerpolicy="no-referrer"
                  src="http://lanhu.htsc.com.cn:8089/images/sketchcb46c72fe6d956701754f6d63df3c87c934b4e02c8e7f67b5f1b0c5f91754acf.png"
                />
                <span class="word26">02517</span>
                <span class="word27">1200</span>
                <span class="info10">+20.49%</span>
              </div>
            </div>
            <div class="bd9">
              <div class="wrap4">
                <span class="word28">远大中国</span>
                <span class="word29">584.35</span>
                <span class="info11">-965.50</span>
              </div>
              <div class="wrap5">
                <img
                  class="label7"
                  referrerpolicy="no-referrer"
                  src="http://lanhu.htsc.com.cn:8089/images/sketchcb46c72fe6d956701754f6d63df3c87c934b4e02c8e7f67b5f1b0c5f91754acf.png"
                />
                <span class="txt8">02789</span> <span class="word30">800</span>
                <span class="info12">-37.49%</span>
              </div>
            </div>
            <div class="bd10">
              <div class="wrap6">
                <span class="word31">经纬天地</span>
                <span class="word32">574.35</span>
                <span class="word33">+365.50</span>
              </div>
              <div class="wrap7">
                <img
                  class="label8"
                  referrerpolicy="no-referrer"
                  src="http://lanhu.htsc.com.cn:8089/images/sketchcb46c72fe6d956701754f6d63df3c87c934b4e02c8e7f67b5f1b0c5f91754acf.png"
                />
                <span class="word34">02961</span>
                <span class="word35">100</span>
                <span class="word36">+9.49%</span>
              </div>
            </div>
            <div class="bd11">
              <div class="box3">
                <span class="word37">经纬天地</span>
                <span class="txt9">463.35</span>
                <span class="info13">+565.50</span>
              </div>
              <div class="box4">
                <img
                  class="icon5"
                  referrerpolicy="no-referrer"
                  src="http://lanhu.htsc.com.cn:8089/images/sketchcb46c72fe6d956701754f6d63df3c87c934b4e02c8e7f67b5f1b0c5f91754acf.png"
                />
                <span class="info14">02961</span>
                <span class="word38">100</span>
                <span class="word39">+3.49%</span>
              </div>
            </div>
          </div>
          <div class="mod13"></div>
        </div>
      </div>
    </div>
  </body>
</html>
