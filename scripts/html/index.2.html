<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Document</title>
    <style>
      body {
        margin: 0;
      }
      ::-webkit-scrollbar {
        display: none;
      }
      button {
        margin: 0;
        padding: 0;
        border: 1px solid transparent;
        outline: 0;
      }
      button:active {
        opacity: 0.6;
      }
      .container {
        position: relative;
        width: 375px;
        height: 812px;
        background-color: #eef0f3;
        overflow: hidden;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }
      .group1 {
        width: 375px;
        height: 812px;
        display: flex;
        flex-direction: column;
      }
      .layer1 {
        height: 44px;
        align-self: center;
        width: 375px;
        justify-content: flex-end;
        padding-bottom: 12px;
        align-items: flex-end;
        padding-right: 14px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .outer1 {
        width: 340px;
        height: 16px;
        flex-direction: row;
        display: flex;
      }
      .box1 {
        width: 54px;
        height: 16px;
        overflow-wrap: break-word;
        text-align: center;
        box-sizing: border-box;
        font-size: 0;
      }
      .word1 {
        font-size: 14px;
        font-family: SFProText-Semibold;
        color: #fff;
        line-height: 16px;
      }
      .info1 {
        font-size: 14px;
        font-family: SFProText-Semibold;
        color: #fff;
        line-height: 16px;
      }
      .box2 {
        width: 17px;
        height: 11px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch8ce7767a969f0a87db754c9e45a3e7a4dcead05688664c56b6305e19b5138508.png);
        background-repeat: no-repeat;
        background-position: -0.6666666666661172px -0.6666666666666288px;
        margin-top: 2px;
        margin-left: 219px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .box3 {
        width: 16px;
        height: 11px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch92d71d13da58ba4887551315bf83a1d7044dd8e56d5d54f8f479185caa02c6e2.png);
        background-repeat: no-repeat;
        background-position: -0.6937274976498884px -0.3306727040325086px;
        margin-top: 2px;
        margin-left: 5px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .icon1 {
        width: 25px;
        height: 12px;
        margin-top: 1px;
        margin-left: 4px;
      }
      .layer2 {
        height: 50px;
        align-self: center;
        width: 375px;
        justify-content: center;
        align-items: center;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .main1 {
        width: 335px;
        height: 42px;
        flex-direction: row;
        display: flex;
      }
      .icon2 {
        width: 30px;
        height: 30px;
        margin-top: 6px;
      }
      .bd1 {
        position: relative;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        overflow: hidden;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch1c79f5ba942235a6c9a96044ee18649db6f54bec2a4ea78b46ab5f88d3e569cb.png);
        background-repeat: no-repeat;
        background-size: 100%;
        margin-top: 25px;
        margin-left: 64px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .group2 {
        position: absolute;
        left: 1px;
        top: 3px;
        width: 15px;
        height: 15px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketche96de86f6f0621ce3d60cfb7e0637a5f7eb6e589df5f7c4085512dd37e29f90a.png);
        background-repeat: no-repeat;
        background-position: -0.8888888888888857px -0.07407407407413302px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .bd2 {
        width: 115px;
        height: 42px;
        margin-left: 3px;
        display: flex;
        flex-direction: column;
      }
      .txt1 {
        width: 68px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 17px;
        text-transform: uppercase;
        font-family: PingFangSC-Medium;
        line-height: 24px;
        text-align: left;
        align-self: flex-start;
        margin-left: 21px;
      }
      .section1 {
        width: 115px;
        height: 18px;
        overflow-wrap: break-word;
        text-align: left;
        align-self: center;
        box-sizing: border-box;
        font-size: 0;
      }
      .word2 {
        font-size: 13px;
        font-family: PingFangSC-Regular;
        color: #030303;
        line-height: 18px;
      }
      .txt2 {
        font-size: 13px;
        font-family: ZLCaiFuTi-Regular;
        color: #030303;
        line-height: 18px;
      }
      .icon3 {
        width: 11px;
        height: 11px;
        margin-top: 28px;
        margin-left: 2px;
      }
      .img1 {
        width: 60px;
        height: 30px;
        margin-top: 6px;
        margin-left: 34px;
      }
      .layer3 {
        width: 236px;
        height: 22px;
        margin-left: 20px;
        margin-top: 10px;
        flex-direction: row;
        display: flex;
        justify-content: space-between;
      }
      .txt3 {
        width: 32px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 16px;
        font-family: PingFangSC-Regular;
        line-height: 22px;
        text-align: left;
      }
      .info2 {
        width: 32px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 16px;
        font-family: PingFangSC-Regular;
        line-height: 22px;
        text-align: left;
      }
      .info3 {
        width: 32px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 16px;
        font-family: PingFangSC-Regular;
        line-height: 22px;
        text-align: left;
      }
      .word3 {
        width: 80px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 16px;
        font-family: PingFangSC-Medium;
        line-height: 22px;
        text-align: left;
      }
      .layer4 {
        width: 8px;
        height: 3px;
        border-radius: 1.5px 1.5px 1.5px 1.5px;
        background-color: #030303;
        align-self: flex-end;
        margin-right: 155px;
        margin-top: 2px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .layer5 {
        width: 335px;
        height: 20px;
        margin-left: 20px;
        margin-top: 20px;
        flex-direction: row;
        display: flex;
      }
      .info4 {
        width: 56px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        line-height: 20px;
        text-align: left;
      }
      .box4 {
        height: 19px;
        border-radius: 4px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch15e4a3055c64c69dcaf9fec864bf3ebefdc2ca759e7739388f35bf2bac7638b1.png);
        background-repeat: no-repeat;
        background-position: -1px -1px;
        margin-left: 6px;
        width: 62px;
        justify-content: center;
        align-items: center;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .box5 {
        width: 51px;
        height: 14px;
        flex-direction: row;
        display: flex;
        justify-content: space-between;
      }
      .main2 {
        height: 11px;
        border-radius: 50%;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch80578d098606722ad71d6b3dc79cf834aeab56ff2a2200215d264044c8ecf15b.png);
        background-repeat: no-repeat;
        background-size: 100%;
        margin-top: 2px;
        width: 11px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .bd3 {
        height: 11px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchb3f7bef1f0ca400868a6c453ab603ae1918cb82828de3527b9bdb2e9b5932d2a.png);
        background-repeat: no-repeat;
        background-size: 100%;
        width: 11px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .label1 {
        width: 11px;
        height: 11px;
      }
      .word4 {
        width: 23px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: left;
      }
      .label2 {
        width: 11px;
        height: 11px;
        margin-top: 2px;
      }
      .txt4 {
        width: 84px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        line-height: 20px;
        text-align: left;
        margin-left: 127px;
      }
      .layer6 {
        width: 335px;
        height: 22px;
        margin-left: 20px;
        margin-top: 6px;
        flex-direction: row;
        display: flex;
        justify-content: space-between;
      }
      .info5 {
        width: 111px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 18px;
        font-family: ZLCaiFuTi-Bold;
        line-height: 26px;
        text-align: left;
      }
      .word5 {
        width: 120px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 18px;
        font-family: ZLCaiFuTi-Bold;
        line-height: 26px;
        text-align: left;
      }
      .layer7 {
        height: 595px;
        border-radius: 20px 20px 0 0;
        background-color: #fff;
        align-self: center;
        margin-top: 18px;
        width: 375px;
        justify-content: flex-start;
        padding-top: 24px;
        align-items: center;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .mod1 {
        width: 335px;
        height: 530px;
        display: flex;
        flex-direction: column;
      }
      .wrap1 {
        width: 335px;
        height: 22px;
        flex-direction: row;
        display: flex;
      }
      .txt5 {
        width: 64px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 16px;
        font-family: PingFangSC-Medium;
        line-height: 22px;
        text-align: left;
      }
      .info6 {
        width: 92px;
        display: block;
        overflow-wrap: break-word;
        color: #a6acb8;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: left;
        margin-top: 3px;
        margin-left: 4px;
      }
      .word6 {
        width: 28px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 14px;
        font-family: PingFangSC-Regular;
        line-height: 20px;
        text-align: left;
        margin-top: 1px;
        margin-left: 133px;
      }
      .label3 {
        width: 14px;
        height: 14px;
        margin-top: 4px;
      }
      .wrap2 {
        height: 127px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchcd015dd0518f188460f1449b972f2250366725fc2b7547fb874191aef5684425.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .layer8 {
        width: 144px;
        height: 21px;
        margin-top: 18px;
        flex-direction: row;
        display: flex;
      }
      .word7 {
        width: 144px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .layer9 {
        width: 150px;
        height: 15px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .bd4 {
        width: 150px;
        height: 15px;
        overflow-wrap: break-word;
        text-align: left;
        box-sizing: border-box;
        font-size: 0;
      }
      .txt6 {
        font-size: 12px;
        font-family: Helvetica;
        color: #6d778b;
        line-height: 14px;
      }
      .info7 {
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        color: #6d778b;
        line-height: 14px;
      }
      .layer10 {
        width: 335px;
        height: 17px;
        margin-top: 12px;
        flex-direction: row;
        display: flex;
      }
      .txt7 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: left;
      }
      .txt8 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: right;
        margin-left: 119px;
      }
      .word8 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: left;
        margin-left: 72px;
      }
      .layer11 {
        width: 335px;
        height: 18px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .txt9 {
        width: 75px;
        display: block;
        overflow-wrap: break-word;
        color: #16213b;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: left;
      }
      .word9 {
        width: 44px;
        display: block;
        overflow-wrap: break-word;
        color: #16213b;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-left: 96px;
      }
      .txt10 {
        width: 75px;
        display: block;
        overflow-wrap: break-word;
        color: #f64843;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-left: 45px;
      }
      .wrap3 {
        height: 127px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchcd015dd0518f188460f1449b972f2250366725fc2b7547fb874191aef5684425.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .box6 {
        width: 144px;
        height: 21px;
        margin-top: 18px;
        flex-direction: row;
        display: flex;
      }
      .info8 {
        width: 144px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .box7 {
        width: 146px;
        height: 14px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .txt11 {
        width: 146px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: left;
      }
      .box8 {
        width: 335px;
        height: 17px;
        margin-top: 13px;
        flex-direction: row;
        display: flex;
      }
      .info9 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: left;
      }
      .word10 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: right;
        margin-left: 119px;
      }
      .word11 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: left;
        margin-left: 72px;
      }
      .box9 {
        width: 335px;
        height: 18px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .word12 {
        width: 75px;
        display: block;
        overflow-wrap: break-word;
        color: #16213b;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: left;
      }
      .word13 {
        width: 44px;
        display: block;
        overflow-wrap: break-word;
        color: #16213b;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-left: 96px;
      }
      .word14 {
        width: 74px;
        display: block;
        overflow-wrap: break-word;
        color: #f64843;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-left: 46px;
      }
      .wrap4 {
        height: 127px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch02b07d80b85e894e695d031bca6790e8d5184172ec5b256412afbb0ec16122d7.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .group3 {
        width: 151px;
        height: 21px;
        margin-top: 18px;
        flex-direction: row;
        display: flex;
      }
      .word15 {
        width: 151px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .group4 {
        width: 146px;
        height: 14px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .word16 {
        width: 146px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 16px;
        text-align: left;
      }
      .group5 {
        width: 335px;
        height: 17px;
        margin-top: 13px;
        flex-direction: row;
        display: flex;
      }
      .word17 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: left;
      }
      .info10 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: right;
        margin-left: 119px;
      }
      .txt12 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: left;
        margin-left: 72px;
      }
      .group6 {
        width: 335px;
        height: 18px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .info11 {
        width: 66px;
        display: block;
        overflow-wrap: break-word;
        color: #16213b;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: left;
      }
      .word18 {
        width: 44px;
        display: block;
        overflow-wrap: break-word;
        color: #16213b;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-left: 105px;
      }
      .word19 {
        width: 66px;
        display: block;
        overflow-wrap: break-word;
        color: #f64843;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-left: 54px;
      }
      .wrap5 {
        height: 127px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketchcd015dd0518f188460f1449b972f2250366725fc2b7547fb874191aef5684425.png);
        background-repeat: no-repeat;
        background-size: 100%;
        align-self: center;
        width: 335px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .block1 {
        width: 144px;
        height: 21px;
        margin-top: 18px;
        flex-direction: row;
        display: flex;
      }
      .word20 {
        width: 144px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
      .block2 {
        width: 150px;
        height: 15px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .main3 {
        width: 150px;
        height: 15px;
        overflow-wrap: break-word;
        text-align: left;
        box-sizing: border-box;
        font-size: 0;
      }
      .word21 {
        font-size: 12px;
        font-family: Helvetica;
        color: #6d778b;
        line-height: 14px;
      }
      .word22 {
        font-size: 12px;
        font-family: ZLCaiFuTi-Regular;
        color: #6d778b;
        line-height: 14px;
      }
      .block3 {
        width: 335px;
        height: 17px;
        margin-top: 12px;
        flex-direction: row;
        display: flex;
      }
      .word23 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: left;
      }
      .info12 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: right;
        margin-left: 119px;
      }
      .txt13 {
        width: 48px;
        display: block;
        overflow-wrap: break-word;
        color: #6d778b;
        font-size: 12px;
        font-family: PingFangSC-Regular;
        line-height: 17px;
        text-align: left;
        margin-left: 72px;
      }
      .block4 {
        width: 335px;
        height: 18px;
        margin-top: 4px;
        flex-direction: row;
        display: flex;
      }
      .txt14 {
        width: 75px;
        display: block;
        overflow-wrap: break-word;
        color: #16213b;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: left;
      }
      .txt15 {
        width: 44px;
        display: block;
        overflow-wrap: break-word;
        color: #16213b;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-left: 96px;
      }
      .word24 {
        width: 75px;
        display: block;
        overflow-wrap: break-word;
        color: #f64843;
        font-size: 15px;
        font-family: ZLCaiFuTi-Regular;
        line-height: 21px;
        text-align: right;
        margin-left: 45px;
      }
      .group7 {
        height: 127px;
        background-image: url(http://lanhu.htsc.com.cn:8089/images/sketch02b07d80b85e894e695d031bca6790e8d5184172ec5b256412afbb0ec16122d7.png);
        background-repeat: no-repeat;
        background-size: 100%;
        width: 335px;
        justify-content: flex-start;
        padding-top: 18px;
        align-items: flex-start;
        position: absolute;
        left: 20px;
        top: 771px;
        display: flex;
        flex-direction: column;
        box-sizing: border-box;
      }
      .word25 {
        width: 144px;
        display: block;
        overflow-wrap: break-word;
        color: #030303;
        font-size: 15px;
        font-family: PingFangSC-Regular;
        line-height: 21px;
        text-align: left;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="group1">
        <div class="layer1">
          <div class="outer1">
            <div class="box1">
              <span class="word1">9:4</span> <span class="info1">1</span>
            </div>
            <div class="box2"></div>
            <div class="box3"></div>
            <img
              class="icon1"
              referrerpolicy="no-referrer"
              src="http://lanhu.htsc.com.cn:8089/images/sketch7c371ddedb0f192c948d81e33b4f6db6925d7eab4e2172f54a9c0421233ea5bd.png"
            />
          </div>
        </div>
        <div class="layer2">
          <div class="main1">
            <img
              class="icon2"
              referrerpolicy="no-referrer"
              src="http://lanhu.htsc.com.cn:8089/images/sketchc5ac05f5f3b629b6a0b0528b7184b03805656947bcee56c097c55e8285df3357.png"
            />
            <div class="bd1"><div class="group2"></div></div>
            <div class="bd2">
              <span class="txt1">我的持仓</span>
              <div class="section1">
                <span class="word2">华泰国际</span>
                <span class="txt2">66***332</span>
              </div>
            </div>
            <img
              class="icon3"
              referrerpolicy="no-referrer"
              src="http://lanhu.htsc.com.cn:8089/images/sketchf51161fb92cb249c793628d80811d0315c41b04d20c43523a6f2504fdc0db7a4.png"
            />
            <img
              class="img1"
              referrerpolicy="no-referrer"
              src="http://lanhu.htsc.com.cn:8089/images/sketch381ed0e136467827a8743d7db6389ce5e682c8bed3dcce271aee9b3e4e9365f1.png"
            />
          </div>
        </div>
        <div class="layer3">
          <span class="txt3">股票</span> <span class="info2">基金</span>
          <span class="info3">债券</span> <span class="word3">结构化产品</span>
        </div>
        <div class="layer4"></div>
        <div class="layer5">
          <span class="info4">持仓总值</span>
          <div class="box4">
            <div class="box5">
              <div class="main2">
                <div class="bd3">
                  <img
                    class="label1"
                    referrerpolicy="no-referrer"
                    src="http://lanhu.htsc.com.cn:8089/images/sketch4cc290c939530284b98b6349b54ffcf38f04ffbf6b08391356611532fd153598.png"
                  />
                </div>
              </div>
              <span class="word4">HKD</span>
              <img
                class="label2"
                referrerpolicy="no-referrer"
                src="http://lanhu.htsc.com.cn:8089/images/sketchf67c3d1f46dae1f9a3900efd76b856bc2573e14e81562ea10ac9a6d42777c9bb.png"
              />
            </div>
          </div>
          <span class="txt4">累计市值变动</span>
        </div>
        <div class="layer6">
          <span class="info5">8,653,240.44</span>
          <span class="word5">+2,326,918.22</span>
        </div>
        <div class="layer7">
          <div class="mod1">
            <div class="wrap1">
              <span class="txt5">全部持仓</span>
              <span class="info6">(单位为结算币种)</span>
              <span class="word6">筛选</span>
              <img
                class="label3"
                referrerpolicy="no-referrer"
                src="http://lanhu.htsc.com.cn:8089/images/sketch41530ed8bb9d2e49af9a8d18362f9f6bc05ec079fb7faa0b968a19a80430b52e.png"
              />
            </div>
            <div class="wrap2">
              <div class="layer8">
                <span class="word7">华润信托掘金信用8号</span>
              </div>
              <div class="layer9">
                <div class="bd4">
                  <span class="txt6">FICC</span>
                  <span class="info7">-FP-CLN-2021-0507</span>
                </div>
              </div>
              <div class="layer10">
                <span class="txt7">持仓金额</span>
                <span class="txt8">持仓份额</span>
                <span class="word8">市值变动</span>
              </div>
              <div class="layer11">
                <span class="txt9">425,134.71</span>
                <span class="word9">50000</span>
                <span class="txt10">+23,211.25</span>
              </div>
            </div>
            <div class="wrap3">
              <div class="box6">
                <span class="info8">华润信托掘金信用6号</span>
              </div>
              <div class="box7">
                <span class="txt11">FICC-FP-CLN-2021-0507</span>
              </div>
              <div class="box8">
                <span class="info9">持仓金额</span>
                <span class="word10">持仓份额</span>
                <span class="word11">持仓收益</span>
              </div>
              <div class="box9">
                <span class="word12">325,134.71</span>
                <span class="word13">50000</span>
                <span class="word14">+10,211.25</span>
              </div>
            </div>
            <div class="wrap4">
              <div class="group3">
                <span class="word15">华润信托掘金信用12号</span>
              </div>
              <div class="group4">
                <span class="word16">FICC-FP-CLN-2021-0507</span>
              </div>
              <div class="group5">
                <span class="word17">持仓金额</span>
                <span class="info10">持仓份额</span>
                <span class="txt12">持仓收益</span>
              </div>
              <div class="group6">
                <span class="info11">25,134.71</span>
                <span class="word18">50000</span>
                <span class="word19">+3,211.25</span>
              </div>
            </div>
            <div class="wrap5">
              <div class="block1">
                <span class="word20">华润信托掘金信用8号</span>
              </div>
              <div class="block2">
                <div class="main3">
                  <span class="word21">FICC</span>
                  <span class="word22">-FP-CLN-2021-0507</span>
                </div>
              </div>
              <div class="block3">
                <span class="word23">持仓金额</span>
                <span class="info12">持仓份额</span>
                <span class="txt13">市值变动</span>
              </div>
              <div class="block4">
                <span class="txt14">425,134.71</span>
                <span class="txt15">50000</span>
                <span class="word24">+23,211.25</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="group7"><span class="word25">华润信托掘金信用6号</span></div>
    </div>
  </body>
</html>
