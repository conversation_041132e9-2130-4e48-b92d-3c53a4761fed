import { Client } from "@langchain/langgraph-sdk";

async function testAssistant() {
  const client = new Client({ apiUrl: "http://localhost:2024" });

  const designToCodeAssistant = await client.assistants.create(
    { graphId: "designToCode", config: {"configurable": {"model_name": "openai"}}}
  );
  
  console.log(designToCodeAssistant);
   
}

testAssistant().catch((error) => {
  console.error("❌ 测试运行失败:", error);
  process.exit(1);
});
