// 资源复制配置文件
export const config = {
  // 需要复制的文件扩展名
  assetExtensions: [
    '.md',      // Markdown 文件
    '.json',    // JSON 配置文件
    '.txt',     // 文本文件
    '.yaml',    // YAML 配置文件
    '.yml',     // YAML 配置文件
    '.html',    // HTML 模板文件
    '.css',     // 样式文件
    '.png',     // 图片文件
    '.jpg',     // 图片文件
    '.jpeg',    // 图片文件
    '.gif',     // 图片文件
    '.svg',     // 矢量图文件
    '.ico',     // 图标文件
    '.woff',    // 字体文件
    '.woff2',   // 字体文件
    '.ttf',     // 字体文件
    '.eot',     // 字体文件
  ],
  
  // 需要忽略的目录
  ignoreDirs: [
    'node_modules',
    'dist',
    '.git',
    '.vscode',
    'coverage',
    '.nyc_output',
    'tmp',
    'temp'
  ],
  
  // 需要忽略的文件模式（支持 glob 模式）
  ignorePatterns: [
    '*.test.*',
    '*.spec.*',
    '*.d.ts',
    '*.map',
    '.DS_Store',
    'Thumbs.db'
  ],
  
  // 扫描的根目录（相对于项目根目录）
  scanRoot: 'src',
  
  // 输出目录（相对于项目根目录）
  outputRoot: 'dist',
  
  // 是否显示详细日志
  verbose: true,
  
  // 是否在复制前清理目标目录中的旧文件
  cleanBeforeCopy: false,
  
  // 是否保持目录结构
  preserveStructure: true
};
