import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { graph } from "../src/graph/designToCode/index.ts";
import type { DesignItem } from "../src/graph/designToCode/types.ts";
import { v4 as uuidv4 } from "uuid";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const html1 = fs.readFileSync(
  path.resolve(__dirname, "html", "index.1.html"),
  "utf-8"
);

const html2 = fs.readFileSync(
  path.resolve(__dirname, "html", "index.2.html"),
  "utf-8"
);
// 测试设计稿数据
const testDesignItems: DesignItem[] = [
  {
    pageName: "首页",
    pageContent: html1,
    type: "html",
  },
  // {
  //   pageName: "产品页",
  //   pageContent: html2,
  //   type: "html",
  // },
];

const testImageDesignItems: DesignItem[] = [
  {
    pageName: "登录页",
    pageContent:
      "base64:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
    type: "img",
  },
];

/**
 * 测试有效HTML设计稿
 */
async function testValidHtmlDesign() {
  console.log("\n🧪 测试有效HTML设计稿...");
  try {
    const startTime = Date.now();

    const result = await graph.invoke(
      {
        input: testDesignItems,
      },
      {
        configurable: { thread_id: uuidv4() },
      }
    );

    const endTime = Date.now();
    const processingTime = endTime - startTime;

    console.log("✅ HTML设计稿测试通过");
    console.log(`处理时间: ${processingTime}ms`);
    console.log(`输入设计稿数量: ${testDesignItems.length}`);
    console.log(
      `生成的HTML结果数量: ${(result.htmlResults as any)?.length || 0}`
    );
    console.log(`合并HTML长度: ${(result.combinedHtml as any)?.length || 0}`);
    console.log(`项目代码长度: ${(result.projectCode as any)?.length || 0}`);
    console.log(`最终输出长度: ${(result.output as any)?.length || 0}`);

    // 显示部分结果
    if (result.htmlResults && (result.htmlResults as any).length > 0) {
      console.log("\n📄 HTML结果预览:");
      (result.htmlResults as any).forEach((item: any, index: number) => {
        console.log(
          `  ${index + 1}. ${item.designPageName}: ${item.html?.substring(0, 100)}...`
        );
      });
    }

    if (result.combinedHtml) {
      console.log("\n🔗 合并HTML预览:");
      console.log((result.combinedHtml as any).substring(0, 200) + "...");
    }

    if (result.projectCode) {
      console.log("\n💻 项目代码预览:");
      console.log((result.projectCode as any).substring(0, 200) + "...");
    }
  } catch (error) {
    console.error("❌ HTML设计稿测试失败:", error);
  }
}

/**
 * 主测试函数
 */
async function runAllTests() {
  console.log("🚀 开始测试 DesignToCode Graph...");
  console.log("=".repeat(50));

  // 测试图的基本结构
  console.log("\n📋 图结构信息:");
  console.log(`图名称: ${graph.name}`);
  console.log(`图类型: ${typeof graph}`);
  console.log(`是否有invoke方法: ${typeof graph.invoke === "function"}`);

  // 运行所有测试
  await testValidHtmlDesign();

  console.log("\n" + "=".repeat(50));
  console.log("✅ 所有测试完成！");
}

runAllTests().catch((error) => {
  console.error("❌ 测试运行失败:", error);
  process.exit(1);
});
