// 简单测试AI编程API
async function testAICodingAPI() {
  try {
    console.log('测试AI编程API...');
    
    const response = await fetch('http://localhost:3001/api/ai-coding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [
          {
            id: '1',
            role: 'user',
            content: '你好，请介绍一下你自己'
          }
        ],
        files: []
      })
    });

    console.log('响应状态:', response.status);
    console.log('响应头:', response.headers);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API错误:', errorText);
      return;
    }

    // 读取流式响应
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    console.log('开始读取流式响应...');
    
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('流式响应结束');
        break;
      }
      
      const chunk = decoder.decode(value);
      console.log('收到数据块:', chunk);
    }
    
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testAICodingAPI();
