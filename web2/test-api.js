// 测试 AI Coding API 的文件上传功能

async function testFileUpload() {
  const testFile = {
    id: "test-1",
    name: "test-file.js",
    content: `// 这是一个测试文件，用于测试 AI 编程助手的文件上传功能

function calculateSum(a, b) {
  return a + b;
}

function calculateProduct(a, b) {
  return a * b;
}

// 这里有一个潜在的性能问题
function inefficientLoop() {
  let result = [];
  for (let i = 0; i < 1000000; i++) {
    result.push(i * 2);
  }
  return result;
}

console.log('测试文件加载完成');`,
    type: "text/javascript"
  };

  const requestBody = {
    messages: [
      {
        id: "test-msg-1",
        role: "user",
        content: "请分析这个 JavaScript 文件，找出潜在的性能问题并提供优化建议"
      }
    ],
    files: [testFile]
  };

  try {
    console.log('发送请求到 AI Coding API...');
    console.log('请求体:', JSON.stringify(requestBody, null, 2));

    const response = await fetch('http://localhost:3001/api/ai-coding', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    console.log('响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API 错误:', errorText);
      return;
    }

    // 处理流式响应
    const reader = response.body?.getReader();
    if (!reader) {
      console.error('无法获取响应流');
      return;
    }

    console.log('开始读取流式响应...');
    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = new TextDecoder().decode(value);
      console.log('收到数据块:', chunk);
    }

    console.log('测试完成');
  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 运行测试
testFileUpload();
