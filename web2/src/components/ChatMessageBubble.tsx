import type { Message } from 'ai/react';
import { MemoizedMarkdown } from './MemoizedMarkdown';
import { cn } from '@/utils/cn';

export function ChatMessageBubble(props: { message: Message; aiEmoji?: string }) {
  return (
    <div
      className={cn(
        `rounded-[24px] max-w-[100%] mb-8 flex`,
        props.message.role === 'user' ? 'bg-secondary text-secondary-foreground px-4 py-2' : null,
        props.message.role === 'user' ? 'ml-auto' : 'mr-auto',
      )}
    >

      <div className="chat-message-bubble whitespace-pre-wrap flex flex-col prose max-w-none overflow-x-auto"> {/* Added overflow-x-auto */}
        <MemoizedMarkdown content={props.message.content} id={props.message.id} />
      </div>
    </div>
  );
}
