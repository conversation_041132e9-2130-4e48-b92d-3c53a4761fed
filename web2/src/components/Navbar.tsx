'use client';

import { usePathname } from 'next/navigation';
import { ReactNode } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button'; // Import Button component

import { cn } from '@/utils/cn';

export const ActiveLink = (props: { href: string; children: ReactNode }) => {
  const pathname = usePathname();
  return (
    <Button
      asChild // Render as a child of the Button component
      variant="ghost" // Use ghost variant for a subtle look
      className={cn(
        'px-4 py-2 rounded-[18px] whitespace-nowrap flex items-center gap-2 text-sm transition-all',
        pathname === props.href && 'bg-primary text-primary-foreground',
      )}
    >
      <Link href={props.href}>
        {props.children}
      </Link>
    </Button>
  );
};

export function Navbar({ title }: { title: string }) {
  return (
    <div className="flex items-center justify-between p-4 bg-black/25 text-white">
      <h1 className="text-lg font-semibold">{title}</h1>
      <nav className="flex items-center gap-2">
        <ActiveLink href="/">
          💬 聊天
        </ActiveLink>
        <ActiveLink href="/ai-coding">
          🤖 AI编程
        </ActiveLink>
      </nav>
    </div>
  );
}
