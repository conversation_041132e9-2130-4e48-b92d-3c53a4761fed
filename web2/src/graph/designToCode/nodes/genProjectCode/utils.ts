/**
 * 验证 HTML 内容是否有效
 */
export function isValidHtmlContent(html: string): boolean {
  return Boolean(
    html && html.trim().length > 0 && html.includes("<") && html.includes(">")
  );
}

/**
 * 分析 HTML 内容结构
 */
export function analyzeHtmlStructure(html: string): {
  hasStyles: boolean;
  hasScripts: boolean;
  hasForms: boolean;
  hasImages: boolean;
  elementCount: number;
} {
  const hasStyles =
    html.includes("style") || html.includes("css") || html.includes("class=");
  const hasScripts =
    html.includes("script") ||
    html.includes("onclick") ||
    html.includes("onload");
  const hasForms =
    html.includes("form") || html.includes("input") || html.includes("button");
  const hasImages = html.includes("img") || html.includes("image");
  const elementCount = (html.match(/<[^>]+>/g) || []).length;

  return {
    hasStyles,
    hasScripts,
    hasForms,
    hasImages,
    elementCount,
  };
}

/**
 * 验证生成的项目代码是否有效
 */
export function validateProjectCode(projectCode: string): {
  valid: boolean;
  error?: string;
} {
  if (!projectCode || !projectCode.trim()) {
    return { valid: false, error: "生成的项目代码为空" };
  }

  // 检查是否包含基本的项目文件
  const requiredFiles = ["package.json", "README.md", "index.html"];
  const missingFiles = requiredFiles.filter(
    (file) => !projectCode.includes(file)
  );

  if (missingFiles.length > 0) {
    return {
      valid: false,
      error: `缺少必要的项目文件: ${missingFiles.join(", ")}`,
    };
  }

  return { valid: true };
}

/**
 * 清理项目代码内容
 */
export function cleanProjectCode(projectCode: string): string {
  return projectCode
    .trim()
    .replace(/\n\s*\n/g, "\n") // 移除多余的空行
    .replace(/\s{2,}/g, " "); // 将多个空白字符替换为单个空格
}

/**
 * 提取项目代码中的文件结构
 */
export function extractProjectStructure(projectCode: string): string[] {
  const filePatterns = [
    /```(\w+):([^\n]+)/g, // ```javascript:filename.js
    /^([^:\n]+\.\w+):/gm, // filename.js:
    /^#\s*([^\n]+\.\w+)/gm, // # filename.js
  ];

  const files: string[] = [];

  filePatterns.forEach((pattern) => {
    const matches = projectCode.match(pattern);
    if (matches) {
      matches.forEach((match) => {
        const fileName = match.replace(/^```\w+:|^#\s*|:$/g, "").trim();
        if (fileName && !files.includes(fileName)) {
          files.push(fileName);
        }
      });
    }
  });

  return files;
}

/**
 * 统计项目代码信息
 */
export function getProjectCodeStats(projectCode: string): {
  totalLength: number;
  fileCount: number;
  hasFrontend: boolean;
  hasBackend: boolean;
  hasConfig: boolean;
} {
  const totalLength = projectCode.length;
  const files = extractProjectStructure(projectCode);
  const fileCount = files.length;

  const hasFrontend =
    projectCode.includes("html") ||
    projectCode.includes("css") ||
    projectCode.includes("javascript") ||
    projectCode.includes("react") ||
    projectCode.includes("vue");
  const hasBackend =
    projectCode.includes("express") ||
    projectCode.includes("node") ||
    projectCode.includes("server") ||
    projectCode.includes("api");
  const hasConfig =
    projectCode.includes("package.json") ||
    projectCode.includes("tsconfig") ||
    projectCode.includes("webpack") ||
    projectCode.includes("vite");

  return {
    totalLength,
    fileCount,
    hasFrontend,
    hasBackend,
    hasConfig,
  };
}
