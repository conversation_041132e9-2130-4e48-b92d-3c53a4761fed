import { StateGraph, MemorySaver, END, START } from "@langchain/langgraph";
import {
  BaseMessage,
  SystemMessage,
  HumanMessage,
} from "@langchain/core/messages";
import { GenProjectCodeState } from "./types";
import { createModel } from "../../model/index";
import {
  getGenProjectCodeSystemPrompt,
  getGenProjectCodeUserPrompt,
} from "./prompt";
import { isValidHtmlContent } from "./utils";

const checkpointer = new MemorySaver();

// 状态对象定义
const GenProjectCodeStateObj = {
  input: {
    value: (x: string, y: string) => y,
    default: () => "",
  },
  output: {
    value: (x: string, y: string) => y,
    default: () => "",
  },
  messages: {
    value: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  },
  error: {
    value: (x: string | undefined, y: string | undefined) => y,
    default: () => undefined,
  },
};

/**
 * 验证输入条件边函数
 */
function validateInput(
  state: GenProjectCodeState
): "genProjectCode" | "failure" {
  console.log("GenProjectCode: 验证输入...");

  // 获取 HTML 内容
  const htmlContent = state.input;

  if (!htmlContent || !htmlContent.trim()) {
    console.log("GenProjectCode: 输入验证失败 - 没有提供 HTML 内容");
    throw new Error("输入验证失败：HTML 内容为空");
  }

  // 验证 HTML 内容
  if (!isValidHtmlContent(htmlContent)) {
    console.log("GenProjectCode: 输入验证失败 - 提供的 HTML 内容无效");
    throw new Error("输入验证失败：HTML 内容无效");
  }

  console.log(
    `GenProjectCode: 输入验证通过 - HTML 内容长度: ${htmlContent.length} 字符`
  );
  return "genProjectCode";
}

/**
 * 生成项目代码节点 - 使用 LLM 生成完整项目
 */
async function genProjectCodeNode(state: GenProjectCodeState) {
  console.log("GenProjectCode: 开始使用 LLM 生成项目代码...");

  const htmlContent = state.input;

  // 创建 LLM 模型
  const model = createModel();

  // 使用提示模板
  const systemPrompt = getGenProjectCodeSystemPrompt();
  const userPrompt = getGenProjectCodeUserPrompt(htmlContent);

  console.log(
    `GenProjectCode: 调用 LLM 生成项目代码，HTML 内容长度: ${htmlContent.length} 字符`
  );

  // 调用 LLM
  const response = await model.invoke([
    new SystemMessage(systemPrompt),
    new HumanMessage(userPrompt),
  ]);

  const projectCode = response.content as string;

  return {
    output: projectCode,
    error: undefined, // 明确设置为 undefined 表示成功
    messages: [
      new SystemMessage(
        `项目代码生成完成，生成了 ${projectCode.length} 字符的代码`
      ),
    ],
  };
}

// 构建 GenProjectCode 工作流图
const workflow = new StateGraph<GenProjectCodeState>({
  channels: GenProjectCodeStateObj,
})
  .addNode("genProjectCode", genProjectCodeNode)
  .addConditionalEdges(START, validateInput, {
    genProjectCode: "genProjectCode",
  })
  .addEdge("genProjectCode", END);

// 编译 GenProjectCode 工作流
export const graph = workflow.compile({ checkpointer });
graph.name = "genProjectCode";

// 导出类型
export type { GenProjectCodeState };
