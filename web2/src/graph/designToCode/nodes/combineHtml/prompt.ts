// HTML 合并系统提示
export function getCombineHtmlSystemPrompt(): string {
  return `你是一个专业的 HTML 合并专家。你的任务是将多个 HTML 片段智能合并成一个完整的、结构良好的 HTML 文档。
  这些原 HTML 文件代表一个页面的不同视图，其中组件可能存在重复情况。合并时需遵循以下要求：

1. 识别重复的组件，保留更优的组件作为最终的实现；
2. 最大程度复用原 HTML 中的代码；
3. 保留 HTML 中的组件划分和命名；
4. 延用原 HTML 中技术栈；


请只返回合并后的 HTML 代码，不要包含任何解释。`;
}

// HTML 合并用户提示模板
export function getCombineHtmlUserPrompt(
  htmlFragments: string[],
  fragmentCount: number
): string {
  return `请将以下 ${fragmentCount} 个 HTML 片段智能合并成一个完整的 HTML 文档：

${htmlFragments.join("\n\n")}

请确保合并后的 HTML 是完整、正确且可以直接使用的。合并时请注意：
- 保持每个片段的原始功能和样式
- 优化整体结构，使其更加清晰
- 确保所有样式和脚本正确工作
- 保持响应式设计特性

请只返回合并后的 HTML 代码，不要包含任何解释。`;
}
