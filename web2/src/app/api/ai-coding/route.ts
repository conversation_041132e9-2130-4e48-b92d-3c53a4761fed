import { NextRequest, NextResponse } from 'next/server';
import { type Message, LangChainAdapter } from 'ai';
import { graph } from "../../../graph/designToCode"
import { DesignItem } from "../../../graph/designToCode/types";
import { v4 as uuidv4 } from "uuid";

import { convertVercelMessageToLangChainMessage } from '@/utils/message-converters';
import { logToolCallsInDevelopment } from '@/utils/stream-logging';

/**
 * This handler initializes and calls the designToCode agent for AI coding tasks.
 * It supports file uploads and converts them to DesignItem format for processing.
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    console.log('AI Coding API: 收到请求', {
      messagesCount: body.messages?.length || 0,
      hasMessages: !!body.messages,
      hasFiles: !!body.files && body.files.length > 0,
      filesCount: body.files?.length || 0
    });

    /**
     * We represent intermediate steps as system messages for display purposes,
     * but don't want them in the chat history.
     */
    const messages = (body.messages ?? [])
      .filter((message: Message) => message.role === 'user' || message.role === 'assistant')
      .map(convertVercelMessageToLangChainMessage);

    console.log('AI Coding API: 处理后的消息', {
      messagesCount: messages.length,
      lastMessage: messages[messages.length - 1]?.content
    });

    // 获取最后一条用户消息作为输入
    const lastUserMessage = messages
      .filter((msg: any) => msg.getType() === 'human')
      .pop();

    let userInput = (lastUserMessage?.content as string) || '';

    // 处理文件上传，转换为DesignItem格式
    const files = body.files || [];
    const designItems: DesignItem[] = [];

    if (files.length > 0) {
      console.log('AI Coding API: 处理上传的文件', { filesCount: files.length });

      // 将文件转换为DesignItem格式
      files.forEach((file: any) => {
        // 根据文件扩展名判断类型
        const fileExtension = file.name.split('.').pop()?.toLowerCase();
        const isImageFile = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(fileExtension || '');

        designItems.push({
          name: file.name,
          content: file.content,
          type: isImageFile ? "img" : "html" // 将代码文件当作html类型处理
        });
      });

      // 如果没有用户输入，提供默认提示
      if (!userInput.trim()) {
        userInput = `请分析以下上传的文件并提供编程建议或优化方案。`;
      }
    }

    // 如果没有文件，创建一个包含用户输入的设计项
    if (designItems.length === 0) {
      if (!userInput.trim()) {
        userInput = "你好！我是AI编程助手，我可以帮助您编写代码、分析代码、调试问题等。请告诉我您需要什么帮助？";
      }

      // 创建一个包含用户输入的设计项
      designItems.push({
        name: "user_request.html",
        content: userInput,
        type: "html"
      });
    }

    /**
     * Stream back all generated tokens and steps from their runs.
     */
    const graphInput = {
      messages: messages,
      input: designItems, // designToCode 期望的是 DesignItem[] 格式
      output: ''
    };

    console.log('AI Coding API: 调用 designToCode graph', {
      userInput: userInput.substring(0, 200) + (userInput.length > 200 ? '...' : ''),
      messagesCount: messages.length,
      designItemsCount: designItems.length
    });

    // 使用 streamEvents 方法，添加thread_id配置
    const threadId = uuidv4();
    const eventStream = graph.streamEvents(graphInput, {
      version: 'v2',
      configurable: { thread_id: threadId }
    });

    // 应用流式日志处理器
    const transformedStream = logToolCallsInDevelopment(eventStream);

    // Adapt the LangChain stream to Vercel AI SDK Stream
    return LangChainAdapter.toDataStreamResponse(transformedStream);
  } catch (e: any) {
    console.error('AI Coding API: 错误', e);
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}
