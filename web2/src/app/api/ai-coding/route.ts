import { NextRequest, NextResponse } from 'next/server';
import { type Message, LangChainAdapter } from 'ai';
import { graph } from "../../../graph/simpleChat"

import { convertVercelMessageToLangChainMessage } from '@/utils/message-converters';
import { logToolCallsInDevelopment } from '@/utils/stream-logging';

/**
 * This handler initializes and calls the simpleChat agent for AI coding tasks.
 * It supports file uploads and coding-related tasks.
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    console.log('AI Coding API: 收到请求', {
      messagesCount: body.messages?.length || 0,
      hasMessages: !!body.messages,
      hasFiles: !!body.files && body.files.length > 0,
      filesCount: body.files?.length || 0
    });

    /**
     * We represent intermediate steps as system messages for display purposes,
     * but don't want them in the chat history.
     */
    const messages = (body.messages ?? [])
      .filter((message: Message) => message.role === 'user' || message.role === 'assistant')
      .map(convertVercelMessageToLangChainMessage);

    console.log('AI Coding API: 处理后的消息', {
      messagesCount: messages.length,
      lastMessage: messages[messages.length - 1]?.content
    });

    // 获取最后一条用户消息作为输入
    const lastUserMessage = messages
      .filter((msg: any) => msg.getType() === 'human')
      .pop();

    let userInput = (lastUserMessage?.content as string) || '';

    // 处理文件上传
    const files = body.files || [];

    if (files.length > 0) {
      console.log('AI Coding API: 处理上传的文件', { filesCount: files.length });

      // 将文件内容添加到用户输入中
      let fileContent = '';
      files.forEach((file: any) => {
        fileContent += `\n\n文件: ${file.name}\n内容:\n${file.content}\n`;
      });

      // 如果没有用户输入，提供默认提示
      if (!userInput.trim()) {
        userInput = `请分析以下上传的文件并提供编程建议或优化方案：${fileContent}`;
      } else {
        userInput = `${userInput}\n\n上传的文件：${fileContent}`;
      }
    }

    // 如果没有任何输入，提供默认提示
    if (!userInput.trim()) {
      userInput = "你好！我是AI编程助手，我可以帮助您编写代码、分析代码、调试问题等。请告诉我您需要什么帮助？";
    }

    /**
     * Stream back all generated tokens and steps from their runs.
     */
    const graphInput = {
      messages: messages,
      input: userInput, // simpleChat 期望的是 string 格式
      output: ''
    };

    console.log('AI Coding API: 调用 simpleChat graph', {
      userInput: userInput.substring(0, 200) + (userInput.length > 200 ? '...' : ''),
      messagesCount: messages.length
    });

    // 使用 streamEvents 方法
    const eventStream = graph.streamEvents(graphInput, {
      version: 'v2'
    });

    // 应用流式日志处理器
    const transformedStream = logToolCallsInDevelopment(eventStream);

    // Adapt the LangChain stream to Vercel AI SDK Stream
    return LangChainAdapter.toDataStreamResponse(transformedStream);
  } catch (e: any) {
    console.error('AI Coding API: 错误', e);
    return NextResponse.json({ error: e.message }, { status: e.status ?? 500 });
  }
}
