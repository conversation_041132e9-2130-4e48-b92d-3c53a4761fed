import { CodingChatWindow } from '@/components/CodingChatWindow';

export default async function AICodingPage() {
  return (
    <CodingChatWindow
      endpoint="api/ai-coding"
      emoji="🤖"
      placeholder="请描述您的编程需求，或上传相关文件..."
      emptyStateComponent={
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="text-6xl mb-4">🤖</div>
          <h1 className="text-2xl font-bold mb-2">AI 编程助手</h1>
          <p className="text-gray-600 mb-4">
            我可以帮助您编写代码、调试问题、解释代码逻辑等
          </p>
          <div className="text-sm text-gray-500 space-y-1">
            <p>• 支持多种编程语言</p>
            <p>• 可以上传文件进行分析</p>
            <p>• 提供代码优化建议</p>
            <p>• 解答编程相关问题</p>
          </div>
        </div>
      }
    />
  );
}
