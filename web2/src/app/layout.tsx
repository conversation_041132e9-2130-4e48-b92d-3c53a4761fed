import './globals.css';
import { Roboto_Mono, Inter } from 'next/font/google';
import { Navbar } from '@/components/Navbar';

const publicSans = Inter({ weight: '400', subsets: ['latin'] });

export default async function RootLayout({ children }: { children: React.ReactNode }) {

  return (
    <html lang="en">
      <body className={publicSans.className} suppressHydrationWarning>
        <div className="bg-secondary grid grid-rows-[auto,1fr] h-[100dvh]">
          <Navbar title="Langgraph MVP" />
          <div className="gradient-up bg-gradient-to-b from-white/10 to-white/0 relative grid border-input border-b-0">
            <div className="absolute inset-0">{children}</div>
          </div>
        </div>
      </body>
    </html>
  );
}
