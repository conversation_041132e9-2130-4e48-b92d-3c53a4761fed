@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 215 20.2% 65.1%;
    --radius: 0.5rem;
  }

}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply font-sans antialiased bg-background text-foreground;
  }

  p {
    margin: 8px 0;
  }

  code {
    @apply text-orange-700;
  }

  li {
    padding: 4px;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .chat-message-bubble a {
    text-decoration: underline;
  }

  .chat-message-bubble ol {
    list-style: decimal;
  }

  .chat-message-bubble ul {
    list-style: disc;
  }

  .chat-message-bubble li {
    white-space: normal;
  }

  .chat-message-bubble pre {
    white-space: pre-wrap;
  }

  .chat-message-bubble blockquote {
    border-left: 4px solid #474545;
    padding-left: 16px;
  }

  .chat-message-bubble hr {
    border-top: 1px solid #474545;
  }

  .chat-message-bubble h1 {
    font-size: x-large;
    font-weight: bold;
  }

  .chat-message-bubble h2 {
    font-size: larger;
    font-weight: bold;
  }

  .chat-message-bubble h3 {
    font-size: large;
    font-weight: bold;
  }

  .chat-message-bubble h4 {
    font-size: medium;
    font-weight: bold;
  }

  .chat-message-bubble h5 {
    font-size: small;
    font-weight: bold;
  }

  .chat-message-bubble h6 {
    font-size: smaller;
    font-weight: bold;
  }

  .chat-message-bubble del {
    text-decoration: line-through;
  }
}
