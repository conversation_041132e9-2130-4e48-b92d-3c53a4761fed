# Langgraph MVP Implementation

This project serves as a template for building AI applications using the Vercel AI SDK, integrated with LangChain/LangGraph for advanced conversational AI capabilities, and includes web search implementation using SerpAPI.

## Features

*   **Vercel AI SDK Integration**: Leverage the power of Vercel's AI SDK for seamless AI model interactions.
*   **LangChain/LangGraph**: Utilize Lang<PERSON>hain and LangGraph for building complex conversational flows and agentic behaviors.
*   **Web Search with SerpAPI**: Incorporate real-time web search capabilities to enhance the AI's knowledge base.
*   **Responsive Chat UI**: A user-friendly chat interface with proper markdown rendering for tables and other formats, consistent alignment, and responsive design.

## Getting Started

To get a local copy up and running, follow these simple steps.

### Prerequisites

*   Node.js (v18 or higher)
*   npm or yarn
*   SerpAPI API Key (for web search)

### Installation

1.  Clone the repository:
    ```bash
    git clone [repository_url]
    cd dia-langchain
    ```
2.  Install dependencies:
    ```bash
    npm install
    # or yarn install
    ```
3.  Create a `.env.local` file based on `.env.example` and add your SerpAPI API key:
    ```
    SERPAPI_API_KEY=your_serpapi_api_key
    OPENAI_API_KEY=your_openai_api_key
    ```
    We're using Open AI's GPT-4.1-Nano. Feel free to change the model or switch to other providers.

### Running the Application

```bash
npm run dev
# or yarn dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the result.

### Live Demo

Experience the application live on Vercel: [vercel-langchain-template-delta.vercel.app](https://vercel-langchain-template-delta.vercel.app)

## Further Implementations

*   **Connect More Tools through Composio**: Extend the AI's capabilities by integrating additional tools and APIs via Composio.
*   **More Complex and Custom LangGraph Implementations**: Develop more sophisticated and tailored LangGraph workflows for advanced use cases.

## License

This project is licensed under the MIT License.
